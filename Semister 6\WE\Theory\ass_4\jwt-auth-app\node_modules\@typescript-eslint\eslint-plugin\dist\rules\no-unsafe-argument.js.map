{"version": 3, "file": "no-unsafe-argument.js", "sourceRoot": "", "sources": ["../../src/rules/no-unsafe-argument.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAC1D,+CAAiC;AAEjC,8CAAgC;AA8BhC,MAAM,iBAAiB;IAGd,MAAM,CAAC,MAAM,CAClB,OAAuB,EACvB,MAA6B;;QAE7B,MAAM,SAAS,GAAG,OAAO,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;QACvD,IAAI,CAAC,SAAS,EAAE;YACd,OAAO,IAAI,CAAC;SACb;QAED,MAAM,UAAU,GAAc,EAAE,CAAC;QACjC,IAAI,QAAQ,GAAoB,IAAI,CAAC;QAErC,MAAM,UAAU,GAAG,SAAS,CAAC,aAAa,EAAE,CAAC;QAC7C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;YAC7C,MAAM,KAAK,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;YAC5B,MAAM,IAAI,GAAG,OAAO,CAAC,yBAAyB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;YAE9D,MAAM,IAAI,GAAG,MAAA,KAAK,CAAC,eAAe,EAAE,0CAAG,CAAC,CAAC,CAAC;YAC1C,IAAI,IAAI,IAAI,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,cAAc,EAAE;gBACvD,kBAAkB;gBAClB,IAAI,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE;oBAC7B,QAAQ,GAAG;wBACT,IAAI,EAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;wBAC7C,IAAI,4BAAoB;wBACxB,KAAK,EAAE,CAAC;qBACT,CAAC;iBACH;qBAAM,IAAI,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE;oBACpC,QAAQ,GAAG;wBACT,aAAa,EAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC;wBACnD,IAAI,4BAAoB;wBACxB,KAAK,EAAE,CAAC;qBACT,CAAC;iBACH;qBAAM;oBACL,QAAQ,GAAG;wBACT,IAAI;wBACJ,IAAI,4BAAoB;wBACxB,KAAK,EAAE,CAAC;qBACT,CAAC;iBACH;gBACD,MAAM;aACP;YAED,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACvB;QAED,OAAO,IAAI,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;IACxC,CAAC;IAID,YACU,UAAqB,EACrB,QAAyB;QADzB,eAAU,GAAV,UAAU,CAAW;QACrB,aAAQ,GAAR,QAAQ,CAAiB;QAtD3B,uBAAkB,GAAG,CAAC,CAAC;QAkDvB,yBAAoB,GAAG,KAAK,CAAC;IAKlC,CAAC;IAEG,oBAAoB;QACzB,MAAM,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC;QACtC,IAAI,CAAC,kBAAkB,IAAI,CAAC,CAAC;QAE7B,IAAI,KAAK,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAChE,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,EAAE;gBACzB,OAAO,IAAI,CAAC;aACb;YAED,QAAQ,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;gBAC1B,+BAAuB,CAAC,CAAC;oBACvB,MAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC;oBAClD,IAAI,IAAI,CAAC,oBAAoB,EAAE;wBAC7B,gEAAgE;wBAChE,uEAAuE;wBACvE,6CAA6C;wBAC7C,wDAAwD;wBACxD,OAAO,aAAa,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;qBAChD;oBAED,MAAM,SAAS,GAAG,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;oBAC9C,IAAI,SAAS,IAAI,aAAa,CAAC,MAAM,EAAE;wBACrC,OAAO,aAAa,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;qBAChD;oBAED,OAAO,aAAa,CAAC,SAAS,CAAC,CAAC;iBACjC;gBAED,gCAAwB;gBACxB;oBACE,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;aAC7B;SACF;QACD,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;IAChC,CAAC;IAEM,yBAAyB;QAC9B,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;IACnC,CAAC;CACF;AAED,kBAAe,IAAI,CAAC,UAAU,CAAiB;IAC7C,IAAI,EAAE,oBAAoB;IAC1B,IAAI,EAAE;QACJ,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EAAE,0DAA0D;YACvE,WAAW,EAAE,OAAO;YACpB,oBAAoB,EAAE,IAAI;SAC3B;QACD,QAAQ,EAAE;YACR,cAAc,EACZ,sFAAsF;YACxF,iBAAiB,EACf,4HAA4H;YAC9H,iBAAiB,EAAE,uCAAuC;YAC1D,YAAY,EAAE,iCAAiC;SAChD;QACD,MAAM,EAAE,EAAE;KACX;IACD,cAAc,EAAE,EAAE;IAClB,MAAM,CAAC,OAAO;QACZ,MAAM,EAAE,OAAO,EAAE,qBAAqB,EAAE,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QAC3E,MAAM,OAAO,GAAG,OAAO,CAAC,cAAc,EAAE,CAAC;QAEzC,OAAO;YACL,+BAA+B,CAC7B,IAAsD;gBAEtD,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;oBAC/B,OAAO;iBACR;gBAED,+DAA+D;gBAC/D,IACE,IAAI,CAAC,aAAa,CAChB,OAAO,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAClE,EACD;oBACA,OAAO;iBACR;gBAED,MAAM,MAAM,GAAG,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBAC/C,MAAM,SAAS,GAAG,iBAAiB,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;gBAC5D,IAAI,CAAC,SAAS,EAAE;oBACd,OAAO;iBACR;gBAED,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,SAAS,EAAE;oBACrC,QAAQ,QAAQ,CAAC,IAAI,EAAE;wBACrB,kBAAkB;wBAClB,KAAK,sBAAc,CAAC,aAAa,CAAC,CAAC;4BACjC,MAAM,aAAa,GAAG,OAAO,CAAC,iBAAiB,CAC7C,qBAAqB,CAAC,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAC7C,CAAC;4BAEF,IAAI,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,EAAE;gCACrC,cAAc;gCACd,OAAO,CAAC,MAAM,CAAC;oCACb,IAAI,EAAE,QAAQ;oCACd,SAAS,EAAE,cAAc;iCAC1B,CAAC,CAAC;6BACJ;iCAAM,IAAI,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE,OAAO,CAAC,EAAE;gCAC1D,gBAAgB;gCAEhB,yFAAyF;gCACzF,OAAO,CAAC,MAAM,CAAC;oCACb,IAAI,EAAE,QAAQ;oCACd,SAAS,EAAE,mBAAmB;iCAC/B,CAAC,CAAC;6BACJ;iCAAM,IAAI,OAAO,CAAC,WAAW,CAAC,aAAa,CAAC,EAAE;gCAC7C,2BAA2B;gCAC3B,MAAM,mBAAmB,GAAG,IAAI,CAAC,gBAAgB,CAC/C,aAAa,EACb,OAAO,CACR,CAAC;gCACF,KAAK,MAAM,SAAS,IAAI,mBAAmB,EAAE;oCAC3C,MAAM,aAAa,GAAG,SAAS,CAAC,oBAAoB,EAAE,CAAC;oCACvD,IAAI,aAAa,IAAI,IAAI,EAAE;wCACzB,SAAS;qCACV;oCACD,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CACpC,SAAS,EACT,aAAa,EACb,OAAO;oCACP,mGAAmG;oCACnG,qBAAqB;oCACrB,IAAI,CACL,CAAC;oCACF,IAAI,MAAM,EAAE;wCACV,OAAO,CAAC,MAAM,CAAC;4CACb,IAAI,EAAE,QAAQ;4CACd,SAAS,EAAE,mBAAmB;4CAC9B,IAAI,EAAE;gDACJ,MAAM,EAAE,OAAO,CAAC,YAAY,CAAC,SAAS,CAAC;gDACvC,QAAQ,EAAE,OAAO,CAAC,YAAY,CAAC,aAAa,CAAC;6CAC9C;yCACF,CAAC,CAAC;qCACJ;iCACF;gCACD,IAAI,aAAa,CAAC,MAAM,CAAC,cAAc,EAAE;oCACvC,gGAAgG;oCAChG,mFAAmF;oCACnF,SAAS,CAAC,yBAAyB,EAAE,CAAC;iCACvC;6BACF;iCAAM;gCACL,4BAA4B;gCAC5B,iEAAiE;gCACjE,sCAAsC;6BACvC;4BACD,MAAM;yBACP;wBAED,OAAO,CAAC,CAAC;4BACP,MAAM,aAAa,GAAG,SAAS,CAAC,oBAAoB,EAAE,CAAC;4BACvD,IAAI,aAAa,IAAI,IAAI,EAAE;gCACzB,SAAS;6BACV;4BAED,MAAM,YAAY,GAAG,OAAO,CAAC,iBAAiB,CAC5C,qBAAqB,CAAC,GAAG,CAAC,QAAQ,CAAC,CACpC,CAAC;4BACF,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CACpC,YAAY,EACZ,aAAa,EACb,OAAO,EACP,QAAQ,CACT,CAAC;4BACF,IAAI,MAAM,EAAE;gCACV,OAAO,CAAC,MAAM,CAAC;oCACb,IAAI,EAAE,QAAQ;oCACd,SAAS,EAAE,gBAAgB;oCAC3B,IAAI,EAAE;wCACJ,MAAM,EAAE,OAAO,CAAC,YAAY,CAAC,YAAY,CAAC;wCAC1C,QAAQ,EAAE,OAAO,CAAC,YAAY,CAAC,aAAa,CAAC;qCAC9C;iCACF,CAAC,CAAC;6BACJ;yBACF;qBACF;iBACF;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}