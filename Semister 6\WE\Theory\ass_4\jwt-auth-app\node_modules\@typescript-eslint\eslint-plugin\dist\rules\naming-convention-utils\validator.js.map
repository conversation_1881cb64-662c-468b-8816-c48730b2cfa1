{"version": 3, "file": "validator.js", "sourceRoot": "", "sources": ["../../../src/rules/naming-convention-utils/validator.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAG1D,iDAAmC;AAEnC,mCAOiB;AACjB,qCAA2D;AAC3D,qCAIkB;AAGlB,SAAS,eAAe,CACtB,IAAqB,EACrB,OAAgB,EAChB,UAAgC;IAIhC,6DAA6D;IAC7D,MAAM,YAAY,GAAG,iBAAS,CAAC,IAAI,CAAC,CAAC;IACrC,MAAM,OAAO,GAAG,UAAU;QACxB,yCAAyC;SACxC,MAAM,CACL,CAAC,CAAC,EAAE,CACF,CAAC,CAAC,CAAC,QAAQ,GAAG,YAAY,CAAC,KAAK,CAAC;QACjC,CAAC,CAAC,QAAQ,KAAK,qBAAa,CAAC,OAAO,CACvC;SACA,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;QACb,IAAI,CAAC,CAAC,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE;YAC7B,8DAA8D;YAC9D,4DAA4D;YAC5D,OAAO,CAAC,CAAC,cAAc,GAAG,CAAC,CAAC,cAAc,CAAC;SAC5C;QAED,MAAM,OAAO,GAAG,IAAA,uBAAc,EAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;QAC3C,MAAM,OAAO,GAAG,IAAA,uBAAc,EAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;QAE3C,uDAAuD;QACvD,IAAI,OAAO,IAAI,CAAC,OAAO,EAAE;YACvB,OAAO,CAAC,CAAC;SACV;QACD,IAAI,CAAC,OAAO,IAAI,OAAO,EAAE;YACvB,OAAO,CAAC,CAAC,CAAC;SACX;QAED,MAAM,mBAAmB,GAAG,IAAA,mCAA0B,EAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;QACnE,MAAM,mBAAmB,GAAG,IAAA,mCAA0B,EAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;QAEnE,mGAAmG;QACnG,IAAI,mBAAmB,IAAI,CAAC,mBAAmB,EAAE;YAC/C,OAAO,CAAC,CAAC,CAAC;SACX;QACD,IAAI,CAAC,mBAAmB,IAAI,mBAAmB,EAAE;YAC/C,OAAO,CAAC,CAAC;SACV;QAED,6BAA6B;QAC7B,6DAA6D;QAC7D,OAAO,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC;IACjC,CAAC,CAAC,CAAC;IAEL,OAAO,CACL,IAAyE,EACzE,YAA4B,IAAI,GAAG,EAAa,EAC1C,EAAE;;QACR,MAAM,YAAY,GAChB,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;YACvC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB;YAC5C,CAAC,CAAC,IAAI,CAAC,IAAI;YACX,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;QAEtB,uDAAuD;QACvD,+EAA+E;QAC/E,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;YAC5B,IAAI,CAAA,MAAA,MAAM,CAAC,MAAM,0CAAE,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,OAAK,MAAA,MAAM,CAAC,MAAM,0CAAE,KAAK,CAAA,EAAE;gBACpE,iCAAiC;gBACjC,SAAS;aACV;YAED,IAAI,MAAA,MAAM,CAAC,SAAS,0CAAE,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE;gBAChE,uCAAuC;gBACvC,SAAS;aACV;YAED,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,YAAY,CAAC,EAAE;gBACvD,0BAA0B;gBAC1B,SAAS;aACV;YAED,IAAI,IAAI,GAAkB,YAAY,CAAC;YAEvC,IAAI,GAAG,kBAAkB,CAAC,SAAS,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC;YACvE,IAAI,IAAI,IAAI,IAAI,EAAE;gBAChB,OAAO;gBACP,OAAO;aACR;YAED,IAAI,GAAG,kBAAkB,CAAC,UAAU,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC;YACxE,IAAI,IAAI,IAAI,IAAI,EAAE;gBAChB,OAAO;gBACP,OAAO;aACR;YAED,IAAI,GAAG,aAAa,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC;YACjE,IAAI,IAAI,IAAI,IAAI,EAAE;gBAChB,OAAO;gBACP,OAAO;aACR;YAED,IAAI,GAAG,aAAa,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC;YACjE,IAAI,IAAI,IAAI,IAAI,EAAE;gBAChB,OAAO;gBACP,OAAO;aACR;YAED,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,YAAY,CAAC,EAAE;gBACrD,OAAO;gBACP,OAAO;aACR;YAED,IACE,CAAC,wBAAwB,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,YAAY,EAAE,SAAS,CAAC,EACtE;gBACA,OAAO;gBACP,OAAO;aACR;YAED,yEAAyE;YACzE,OAAO;SACR;IACH,CAAC,CAAC;IAEF,uDAAuD;IACvD,SAAS,gBAAgB,CAAC,EACxB,OAAO,EACP,OAAO,EACP,YAAY,EACZ,aAAa,EACb,QAAQ,EACR,MAAM,EACN,KAAK,GASN;;QACC,OAAO;YACL,IAAI,EAAE,IAAA,oCAA2B,EAAC,IAAI,CAAC;YACvC,IAAI,EAAE,YAAY;YAClB,aAAa;YACb,QAAQ;YACR,KAAK;YACL,OAAO,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,CAAC,IAAI,CAAC;YAC5B,OAAO,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,yBAAiB,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC;YAC3D,KAAK,EAAE,MAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,KAAK,0CAAE,QAAQ,EAAE;YAChC,UAAU,EACR,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,KAAK,MAAK,IAAI;gBACpB,CAAC,CAAC,OAAO;gBACT,CAAC,CAAC,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,KAAK,MAAK,KAAK;oBACzB,CAAC,CAAC,WAAW;oBACb,CAAC,CAAC,IAAI;SACX,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,SAAS,kBAAkB,CACzB,QAAgC,EAChC,MAA0B,EAC1B,IAAY,EACZ,IAAyE,EACzE,YAAoB;QAEpB,MAAM,MAAM,GACV,QAAQ,KAAK,SAAS;YACpB,CAAC,CAAC,MAAM,CAAC,iBAAiB;YAC1B,CAAC,CAAC,MAAM,CAAC,kBAAkB,CAAC;QAChC,IAAI,CAAC,MAAM,EAAE;YACX,OAAO,IAAI,CAAC;SACb;QAED,MAAM,mBAAmB,GACvB,QAAQ,KAAK,SAAS;YACpB,CAAC,CAAC,GAAY,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC;YACrC,CAAC,CAAC,GAAY,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QACxC,MAAM,oBAAoB,GACxB,QAAQ,KAAK,SAAS;YACpB,CAAC,CAAC,GAAW,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;YAC7B,CAAC,CAAC,GAAW,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAEtC,MAAM,mBAAmB,GACvB,QAAQ,KAAK,SAAS;YACpB,CAAC,CAAC,GAAY,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;YACtC,CAAC,CAAC,GAAY,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QACzC,MAAM,oBAAoB,GACxB,QAAQ,KAAK,SAAS;YACpB,CAAC,CAAC,GAAW,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;YAC7B,CAAC,CAAC,GAAW,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAEtC,QAAQ,MAAM,EAAE;YACd,sEAAsE;YACtE,KAAK,yBAAiB,CAAC,KAAK,CAAC,CAAC;gBAC5B,IAAI,mBAAmB,EAAE,EAAE;oBACzB,OAAO,oBAAoB,EAAE,CAAC;iBAC/B;gBAED,OAAO,IAAI,CAAC;aACb;YAED,KAAK,yBAAiB,CAAC,WAAW,CAAC,CAAC;gBAClC,IAAI,mBAAmB,EAAE,EAAE;oBACzB,OAAO,oBAAoB,EAAE,CAAC;iBAC/B;gBAED,OAAO,IAAI,CAAC;aACb;YAED,KAAK,yBAAiB,CAAC,mBAAmB,CAAC,CAAC;gBAC1C,IAAI,mBAAmB,EAAE,EAAE;oBACzB,OAAO,oBAAoB,EAAE,CAAC;iBAC/B;gBAED,IAAI,mBAAmB,EAAE,EAAE;oBACzB,OAAO,oBAAoB,EAAE,CAAC;iBAC/B;gBAED,OAAO,IAAI,CAAC;aACb;YAED,SAAS;YACT,KAAK,yBAAiB,CAAC,MAAM,CAAC,CAAC;gBAC7B,IAAI,mBAAmB,EAAE,EAAE;oBACzB,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI;wBACJ,SAAS,EAAE,sBAAsB;wBACjC,IAAI,EAAE,gBAAgB,CAAC;4BACrB,YAAY;4BACZ,QAAQ;4BACR,KAAK,EAAE,KAAK;yBACb,CAAC;qBACH,CAAC,CAAC;oBACH,OAAO,IAAI,CAAC;iBACb;gBAED,OAAO,IAAI,CAAC;aACb;YAED,UAAU;YACV,KAAK,yBAAiB,CAAC,OAAO,CAAC,CAAC;gBAC9B,IAAI,CAAC,mBAAmB,EAAE,EAAE;oBAC1B,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI;wBACJ,SAAS,EAAE,mBAAmB;wBAC9B,IAAI,EAAE,gBAAgB,CAAC;4BACrB,YAAY;4BACZ,QAAQ;4BACR,KAAK,EAAE,KAAK;yBACb,CAAC;qBACH,CAAC,CAAC;oBACH,OAAO,IAAI,CAAC;iBACb;gBAED,OAAO,oBAAoB,EAAE,CAAC;aAC/B;YAED,KAAK,yBAAiB,CAAC,aAAa,CAAC,CAAC;gBACpC,IAAI,CAAC,mBAAmB,EAAE,EAAE;oBAC1B,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI;wBACJ,SAAS,EAAE,mBAAmB;wBAC9B,IAAI,EAAE,gBAAgB,CAAC;4BACrB,YAAY;4BACZ,QAAQ;4BACR,KAAK,EAAE,KAAK;yBACb,CAAC;qBACH,CAAC,CAAC;oBACH,OAAO,IAAI,CAAC;iBACb;gBAED,OAAO,oBAAoB,EAAE,CAAC;aAC/B;SACF;IACH,CAAC;IAED;;OAEG;IACH,SAAS,aAAa,CACpB,QAA6B,EAC7B,MAA0B,EAC1B,IAAY,EACZ,IAAyE,EACzE,YAAoB;QAEpB,MAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;QACjC,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;YACpC,OAAO,IAAI,CAAC;SACb;QAED,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE;YAC3B,MAAM,QAAQ,GACZ,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YACxE,MAAM,SAAS,GACb,QAAQ,KAAK,QAAQ;gBACnB,CAAC,CAAC,GAAW,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC;gBACxC,CAAC,CAAC,GAAW,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YAEjD,IAAI,QAAQ,EAAE;gBACZ,iCAAiC;gBACjC,OAAO,SAAS,EAAE,CAAC;aACpB;SACF;QAED,OAAO,CAAC,MAAM,CAAC;YACb,IAAI;YACJ,SAAS,EAAE,cAAc;YACzB,IAAI,EAAE,gBAAgB,CAAC;gBACrB,YAAY;gBACZ,QAAQ;gBACR,OAAO;aACR,CAAC;SACH,CAAC,CAAC;QACH,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,SAAS,cAAc,CACrB,MAA0B,EAC1B,IAAY,EACZ,IAAyE,EACzE,YAAoB;QAEpB,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;QAC7B,IAAI,CAAC,MAAM,EAAE;YACX,OAAO,IAAI,CAAC;SACb;QAED,MAAM,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvC,IAAI,MAAM,CAAC,KAAK,IAAI,MAAM,EAAE;YAC1B,OAAO,IAAI,CAAC;SACb;QACD,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,MAAM,EAAE;YAC5B,OAAO,IAAI,CAAC;SACb;QAED,OAAO,CAAC,MAAM,CAAC;YACb,IAAI;YACJ,SAAS,EAAE,eAAe;YAC1B,IAAI,EAAE,gBAAgB,CAAC;gBACrB,YAAY;gBACZ,MAAM;aACP,CAAC;SACH,CAAC,CAAC;QACH,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACH,SAAS,wBAAwB,CAC/B,MAA0B,EAC1B,IAAY,EACZ,IAAyE,EACzE,YAAoB,EACpB,SAAyB;QAEzB,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;QAC9B,IAAI,CAAC,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM,CAAA,EAAE;YACpB,OAAO,IAAI,CAAC;SACb;QAED,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,iBAAS,CAAC,cAAc,CAAC,EAAE;YAC5C,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;gBAC5B,MAAM,OAAO,GAAG,wCAA+B,CAAC,MAAM,CAAC,CAAC;gBACxD,IAAI,OAAO,CAAC,IAAI,CAAC,EAAE;oBACjB,OAAO,IAAI,CAAC;iBACb;aACF;SACF;QAED,OAAO,CAAC,MAAM,CAAC;YACb,IAAI;YACJ,SAAS,EACP,YAAY,KAAK,IAAI;gBACnB,CAAC,CAAC,oBAAoB;gBACtB,CAAC,CAAC,2BAA2B;YACjC,IAAI,EAAE,gBAAgB,CAAC;gBACrB,YAAY;gBACZ,aAAa,EAAE,IAAI;gBACnB,OAAO;aACR,CAAC;SACH,CAAC,CAAC;QACH,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC;AAqFQ,0CAAe;AAnFxB,MAAM,2BAA2B,GAC/B,iBAAS,CAAC,QAAQ;IAClB,iBAAS,CAAC,SAAS;IACnB,iBAAS,CAAC,aAAa;IACvB,iBAAS,CAAC,qBAAqB;IAC/B,iBAAS,CAAC,YAAY;IACtB,iBAAS,CAAC,iBAAiB;IAC3B,iBAAS,CAAC,QAAQ,CAAC;AAErB,SAAS,aAAa,CACpB,IAAmB,EACnB,MAA0B,EAC1B,OAAgB,EAChB,QAAmB;IAEnB,IAAI,MAAM,CAAC,KAAK,IAAI,IAAI,EAAE;QACxB,OAAO,IAAI,CAAC;KACb;IAED,IAAI,CAAC,2BAA2B,GAAG,QAAQ,CAAC,KAAK,CAAC,EAAE;QAClD,OAAO,IAAI,CAAC;KACb;IAED,MAAM,EAAE,qBAAqB,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;IAC3E,MAAM,OAAO,GAAG,OAAO,CAAC,cAAc,EAAE,CAAC;IACzC,MAAM,MAAM,GAAG,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAC/C,MAAM,IAAI,GAAG,OAAO;SACjB,iBAAiB,CAAC,MAAM,CAAC;QAC1B,0EAA0E;SACzE,kBAAkB,EAAE,CAAC;IAExB,KAAK,MAAM,WAAW,IAAI,MAAM,CAAC,KAAK,EAAE;QACtC,QAAQ,WAAW,EAAE;YACnB,KAAK,qBAAa,CAAC,KAAK;gBACtB,IACE,eAAe,CACb,IAAI,EACJ,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CACtD,EACD;oBACA,OAAO,IAAI,CAAC;iBACb;gBACD,MAAM;YAER,KAAK,qBAAa,CAAC,QAAQ;gBACzB,IAAI,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE;oBAChE,OAAO,IAAI,CAAC;iBACb;gBACD,MAAM;YAER,KAAK,qBAAa,CAAC,OAAO,CAAC;YAC3B,KAAK,qBAAa,CAAC,MAAM,CAAC;YAC1B,KAAK,qBAAa,CAAC,MAAM,CAAC,CAAC;gBACzB,MAAM,UAAU,GAAG,OAAO,CAAC,YAAY;gBACrC,+EAA+E;gBAC/E,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC,CAC/D,CAAC;gBACF,MAAM,iBAAiB,GAAG,qBAAa,CAAC,WAAW,CAAC,CAAC;gBACrD,IAAI,UAAU,KAAK,iBAAiB,EAAE;oBACpC,OAAO,IAAI,CAAC;iBACb;gBACD,MAAM;aACP;SACF;KACF;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;GAEG;AACH,SAAS,eAAe,CACtB,IAAa,EACb,EAA8B;IAE9B,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE;QAClB,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;KACrC;IAED,OAAO,EAAE,CAAC,IAAI,CAAC,CAAC;AAClB,CAAC"}