{"ast": null, "code": "var _jsxFileName = \"D:\\\\University\\\\Semister 6\\\\WE\\\\Theory\\\\jwt-auth-app\\\\src\\\\components\\\\Register.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link } from 'react-router-dom';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Register = ({\n  setAuth\n}) => {\n  _s();\n  const [inputs, setInputs] = useState({\n    username: '',\n    password: ''\n  });\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const {\n    username,\n    password\n  } = inputs;\n  const onChange = e => {\n    setInputs({\n      ...inputs,\n      [e.target.name]: e.target.value\n    });\n  };\n  const onSubmitForm = async e => {\n    e.preventDefault();\n    try {\n      const response = await axios.post('http://localhost:4000/register', {\n        username,\n        password\n      });\n      setSuccess(response.data.message);\n      setError('');\n\n      // Clear form\n      setInputs({\n        username: '',\n        password: ''\n      });\n    } catch (err) {\n      console.error(err.response.data);\n      setError(err.response.data.message || 'An error occurred');\n      setSuccess('');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"text-center my-5\",\n      children: \"Register\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"alert alert-danger\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 17\n    }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"alert alert-success\",\n      children: success\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 19\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: onSubmitForm,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"username\",\n          className: \"form-label\",\n          children: \"Username\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          name: \"username\",\n          placeholder: \"Username\",\n          className: \"form-control\",\n          value: username,\n          onChange: onChange,\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"password\",\n          className: \"form-label\",\n          children: \"Password\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"password\",\n          name: \"password\",\n          placeholder: \"Password\",\n          className: \"form-control\",\n          value: password,\n          onChange: onChange,\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-primary btn-block\",\n        children: \"Register\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"mt-3\",\n      children: [\"Already have an account? \", /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/login\",\n        children: \"Login\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 34\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 44,\n    columnNumber: 5\n  }, this);\n};\n_s(Register, \"TsIsxJUDUyKC6DUgdrVQi+PuvL0=\");\n_c = Register;\nexport default Register;\nvar _c;\n$RefreshReg$(_c, \"Register\");", "map": {"version": 3, "names": ["React", "useState", "Link", "axios", "jsxDEV", "_jsxDEV", "Register", "setAuth", "_s", "inputs", "setInputs", "username", "password", "error", "setError", "success", "setSuccess", "onChange", "e", "target", "name", "value", "onSubmitForm", "preventDefault", "response", "post", "data", "message", "err", "console", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "htmlFor", "type", "placeholder", "required", "to", "_c", "$RefreshReg$"], "sources": ["D:/University/Semister 6/WE/Theory/jwt-auth-app/src/components/Register.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link } from 'react-router-dom';\nimport axios from 'axios';\n\nconst Register = ({ setAuth }) => {\n  const [inputs, setInputs] = useState({\n    username: '',\n    password: ''\n  });\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n\n  const { username, password } = inputs;\n\n  const onChange = (e) => {\n    setInputs({ ...inputs, [e.target.name]: e.target.value });\n  };\n\n  const onSubmitForm = async (e) => {\n    e.preventDefault();\n    try {\n      const response = await axios.post('http://localhost:4000/register', {\n        username,\n        password\n      });\n\n      setSuccess(response.data.message);\n      setError('');\n      \n      // Clear form\n      setInputs({\n        username: '',\n        password: ''\n      });\n      \n    } catch (err) {\n      console.error(err.response.data);\n      setError(err.response.data.message || 'An error occurred');\n      setSuccess('');\n    }\n  };\n\n  return (\n    <div className=\"container\">\n      <h1 className=\"text-center my-5\">Register</h1>\n      {error && <div className=\"alert alert-danger\">{error}</div>}\n      {success && <div className=\"alert alert-success\">{success}</div>}\n      <form onSubmit={onSubmitForm}>\n        <div className=\"mb-3\">\n          <label htmlFor=\"username\" className=\"form-label\">Username</label>\n          <input\n            type=\"text\"\n            name=\"username\"\n            placeholder=\"Username\"\n            className=\"form-control\"\n            value={username}\n            onChange={onChange}\n            required\n          />\n        </div>\n        <div className=\"mb-3\">\n          <label htmlFor=\"password\" className=\"form-label\">Password</label>\n          <input\n            type=\"password\"\n            name=\"password\"\n            placeholder=\"Password\"\n            className=\"form-control\"\n            value={password}\n            onChange={onChange}\n            required\n          />\n        </div>\n        <button className=\"btn btn-primary btn-block\">Register</button>\n      </form>\n      <p className=\"mt-3\">\n        Already have an account? <Link to=\"/login\">Login</Link>\n      </p>\n    </div>\n  );\n};\n\nexport default Register;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,QAAQ,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAChC,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGT,QAAQ,CAAC;IACnCU,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAE1C,MAAM;IAAEU,QAAQ;IAAEC;EAAS,CAAC,GAAGH,MAAM;EAErC,MAAMQ,QAAQ,GAAIC,CAAC,IAAK;IACtBR,SAAS,CAAC;MAAE,GAAGD,MAAM;MAAE,CAACS,CAAC,CAACC,MAAM,CAACC,IAAI,GAAGF,CAAC,CAACC,MAAM,CAACE;IAAM,CAAC,CAAC;EAC3D,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOJ,CAAC,IAAK;IAChCA,CAAC,CAACK,cAAc,CAAC,CAAC;IAClB,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMrB,KAAK,CAACsB,IAAI,CAAC,gCAAgC,EAAE;QAClEd,QAAQ;QACRC;MACF,CAAC,CAAC;MAEFI,UAAU,CAACQ,QAAQ,CAACE,IAAI,CAACC,OAAO,CAAC;MACjCb,QAAQ,CAAC,EAAE,CAAC;;MAEZ;MACAJ,SAAS,CAAC;QACRC,QAAQ,EAAE,EAAE;QACZC,QAAQ,EAAE;MACZ,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAOgB,GAAG,EAAE;MACZC,OAAO,CAAChB,KAAK,CAACe,GAAG,CAACJ,QAAQ,CAACE,IAAI,CAAC;MAChCZ,QAAQ,CAACc,GAAG,CAACJ,QAAQ,CAACE,IAAI,CAACC,OAAO,IAAI,mBAAmB,CAAC;MAC1DX,UAAU,CAAC,EAAE,CAAC;IAChB;EACF,CAAC;EAED,oBACEX,OAAA;IAAKyB,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxB1B,OAAA;MAAIyB,SAAS,EAAC,kBAAkB;MAAAC,QAAA,EAAC;IAAQ;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EAC7CtB,KAAK,iBAAIR,OAAA;MAAKyB,SAAS,EAAC,oBAAoB;MAAAC,QAAA,EAAElB;IAAK;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,EAC1DpB,OAAO,iBAAIV,OAAA;MAAKyB,SAAS,EAAC,qBAAqB;MAAAC,QAAA,EAAEhB;IAAO;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAChE9B,OAAA;MAAM+B,QAAQ,EAAEd,YAAa;MAAAS,QAAA,gBAC3B1B,OAAA;QAAKyB,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnB1B,OAAA;UAAOgC,OAAO,EAAC,UAAU;UAACP,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACjE9B,OAAA;UACEiC,IAAI,EAAC,MAAM;UACXlB,IAAI,EAAC,UAAU;UACfmB,WAAW,EAAC,UAAU;UACtBT,SAAS,EAAC,cAAc;UACxBT,KAAK,EAAEV,QAAS;UAChBM,QAAQ,EAAEA,QAAS;UACnBuB,QAAQ;QAAA;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACN9B,OAAA;QAAKyB,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnB1B,OAAA;UAAOgC,OAAO,EAAC,UAAU;UAACP,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACjE9B,OAAA;UACEiC,IAAI,EAAC,UAAU;UACflB,IAAI,EAAC,UAAU;UACfmB,WAAW,EAAC,UAAU;UACtBT,SAAS,EAAC,cAAc;UACxBT,KAAK,EAAET,QAAS;UAChBK,QAAQ,EAAEA,QAAS;UACnBuB,QAAQ;QAAA;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACN9B,OAAA;QAAQyB,SAAS,EAAC,2BAA2B;QAAAC,QAAA,EAAC;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3D,CAAC,eACP9B,OAAA;MAAGyB,SAAS,EAAC,MAAM;MAAAC,QAAA,GAAC,2BACO,eAAA1B,OAAA,CAACH,IAAI;QAACuC,EAAE,EAAC,QAAQ;QAAAV,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEV,CAAC;AAAC3B,EAAA,CA3EIF,QAAQ;AAAAoC,EAAA,GAARpC,QAAQ;AA6Ed,eAAeA,QAAQ;AAAC,IAAAoC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}