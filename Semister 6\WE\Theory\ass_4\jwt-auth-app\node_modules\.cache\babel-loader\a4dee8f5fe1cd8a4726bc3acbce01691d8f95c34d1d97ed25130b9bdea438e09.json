{"ast": null, "code": "var _jsxFileName = \"D:\\\\University\\\\Semister 6\\\\WE\\\\Theory\\\\jwt-auth-app\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport Login from './components/Login';\nimport Register from './components/Register';\nimport Dashboard from './components/Dashboard';\nimport Navbar from './components/Navbar';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  useEffect(() => {\n    // Check if token exists in localStorage\n    const token = localStorage.getItem('token');\n    if (token) {\n      setIsAuthenticated(true);\n    }\n  }, []);\n  const setAuth = boolean => {\n    setIsAuthenticated(boolean);\n  };\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"App\",\n      children: [/*#__PURE__*/_jsxDEV(Navbar, {\n        isAuthenticated: isAuthenticated,\n        setAuth: setAuth\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mt-4\",\n        children: /*#__PURE__*/_jsxDEV(Routes, {\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/login\",\n            element: !isAuthenticated ? /*#__PURE__*/_jsxDEV(Login, {\n              setAuth: setAuth\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 31,\n              columnNumber: 43\n            }, this) : /*#__PURE__*/_jsxDEV(Navigate, {\n              to: \"/dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 31,\n              columnNumber: 73\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/register\",\n            element: !isAuthenticated ? /*#__PURE__*/_jsxDEV(Register, {\n              setAuth: setAuth\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 35,\n              columnNumber: 43\n            }, this) : /*#__PURE__*/_jsxDEV(Navigate, {\n              to: \"/dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 35,\n              columnNumber: 76\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 33,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/dashboard\",\n            element: isAuthenticated ? /*#__PURE__*/_jsxDEV(Dashboard, {\n              setAuth: setAuth\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 39,\n              columnNumber: 42\n            }, this) : /*#__PURE__*/_jsxDEV(Navigate, {\n              to: \"/login\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 39,\n              columnNumber: 76\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/\",\n            element: /*#__PURE__*/_jsxDEV(Navigate, {\n              to: isAuthenticated ? \"/dashboard\" : \"/login\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 43,\n              columnNumber: 24\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 24,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"JkS3Meyzlj18m4l86SBr9YDqEkQ=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "<PERSON><PERSON>", "Register", "Dashboard", "<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "App", "_s", "isAuthenticated", "setIsAuthenticated", "token", "localStorage", "getItem", "setAuth", "boolean", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "to", "_c", "$RefreshReg$"], "sources": ["D:/University/Semister 6/WE/Theory/jwt-auth-app/src/App.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport Login from './components/Login';\nimport Register from './components/Register';\nimport Dashboard from './components/Dashboard';\nimport Navbar from './components/Navbar';\n\nfunction App() {\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  \n  useEffect(() => {\n    // Check if token exists in localStorage\n    const token = localStorage.getItem('token');\n    if (token) {\n      setIsAuthenticated(true);\n    }\n  }, []);\n\n  const setAuth = (boolean) => {\n    setIsAuthenticated(boolean);\n  };\n\n  return (\n    <Router>\n      <div className=\"App\">\n        <Navbar isAuthenticated={isAuthenticated} setAuth={setAuth} />\n        <div className=\"container mt-4\">\n          <Routes>\n            <Route \n              path=\"/login\" \n              element={!isAuthenticated ? <Login setAuth={setAuth} /> : <Navigate to=\"/dashboard\" />} \n            />\n            <Route \n              path=\"/register\" \n              element={!isAuthenticated ? <Register setAuth={setAuth} /> : <Navigate to=\"/dashboard\" />} \n            />\n            <Route \n              path=\"/dashboard\" \n              element={isAuthenticated ? <Dashboard setAuth={setAuth} /> : <Navigate to=\"/login\" />} \n            />\n            <Route \n              path=\"/\" \n              element={<Navigate to={isAuthenticated ? \"/dashboard\" : \"/login\"} />} \n            />\n          </Routes>\n        </div>\n      </div>\n    </Router>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,OAAOC,KAAK,MAAM,oBAAoB;AACtC,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,MAAM,MAAM,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAE7DC,SAAS,CAAC,MAAM;IACd;IACA,MAAMgB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAIF,KAAK,EAAE;MACTD,kBAAkB,CAAC,IAAI,CAAC;IAC1B;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMI,OAAO,GAAIC,OAAO,IAAK;IAC3BL,kBAAkB,CAACK,OAAO,CAAC;EAC7B,CAAC;EAED,oBACET,OAAA,CAACT,MAAM;IAAAmB,QAAA,eACLV,OAAA;MAAKW,SAAS,EAAC,KAAK;MAAAD,QAAA,gBAClBV,OAAA,CAACF,MAAM;QAACK,eAAe,EAAEA,eAAgB;QAACK,OAAO,EAAEA;MAAQ;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC9Df,OAAA;QAAKW,SAAS,EAAC,gBAAgB;QAAAD,QAAA,eAC7BV,OAAA,CAACR,MAAM;UAAAkB,QAAA,gBACLV,OAAA,CAACP,KAAK;YACJuB,IAAI,EAAC,QAAQ;YACbC,OAAO,EAAE,CAACd,eAAe,gBAAGH,OAAA,CAACL,KAAK;cAACa,OAAO,EAAEA;YAAQ;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGf,OAAA,CAACN,QAAQ;cAACwB,EAAE,EAAC;YAAY;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxF,CAAC,eACFf,OAAA,CAACP,KAAK;YACJuB,IAAI,EAAC,WAAW;YAChBC,OAAO,EAAE,CAACd,eAAe,gBAAGH,OAAA,CAACJ,QAAQ;cAACY,OAAO,EAAEA;YAAQ;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGf,OAAA,CAACN,QAAQ;cAACwB,EAAE,EAAC;YAAY;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3F,CAAC,eACFf,OAAA,CAACP,KAAK;YACJuB,IAAI,EAAC,YAAY;YACjBC,OAAO,EAAEd,eAAe,gBAAGH,OAAA,CAACH,SAAS;cAACW,OAAO,EAAEA;YAAQ;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGf,OAAA,CAACN,QAAQ;cAACwB,EAAE,EAAC;YAAQ;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvF,CAAC,eACFf,OAAA,CAACP,KAAK;YACJuB,IAAI,EAAC,GAAG;YACRC,OAAO,eAAEjB,OAAA,CAACN,QAAQ;cAACwB,EAAE,EAAEf,eAAe,GAAG,YAAY,GAAG;YAAS;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb;AAACb,EAAA,CA1CQD,GAAG;AAAAkB,EAAA,GAAHlB,GAAG;AA4CZ,eAAeA,GAAG;AAAC,IAAAkB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}