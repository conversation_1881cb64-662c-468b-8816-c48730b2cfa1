import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import Login from './components/Login';
import Register from './components/Register';
import Dashboard from './components/Dashboard';
import Navbar from './components/Navbar';

function App() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  
  useEffect(() => {
    // Check if token exists in localStorage
    const token = localStorage.getItem('token');
    if (token) {
      setIsAuthenticated(true);
    }
  }, []);

  const setAuth = (boolean) => {
    setIsAuthenticated(boolean);
  };

  return (
    <Router>
      <div className="app-container">
        <Navbar isAuthenticated={isAuthenticated} setAuth={setAuth} />
        <main className="main-content">
          <div className="container">
            <Routes>
              <Route
                path="/login"
                element={!isAuthenticated ? <Login setAuth={setAuth} /> : <Navigate to="/dashboard" />}
              />
              <Route
                path="/register"
                element={!isAuthenticated ? <Register setAuth={setAuth} /> : <Navigate to="/dashboard" />}
              />
              <Route
                path="/dashboard"
                element={isAuthenticated ? <Dashboard setAuth={setAuth} /> : <Navigate to="/login" />}
              />
              <Route
                path="/"
                element={<Navigate to={isAuthenticated ? "/dashboard" : "/login"} />}
              />
            </Routes>
          </div>
        </main>
      </div>
    </Router>
  );
}

export default App;
