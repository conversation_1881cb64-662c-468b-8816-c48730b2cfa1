{"ast": null, "code": "var _jsxFileName = \"D:\\\\University\\\\Semister 6\\\\WE\\\\Theory\\\\ass_4\\\\jwt-auth-app\\\\src\\\\components\\\\Login.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link } from 'react-router-dom';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Login = ({\n  setAuth\n}) => {\n  _s();\n  const [inputs, setInputs] = useState({\n    username: '',\n    password: ''\n  });\n  const [error, setError] = useState('');\n  const {\n    username,\n    password\n  } = inputs;\n  const onChange = e => {\n    setInputs({\n      ...inputs,\n      [e.target.name]: e.target.value\n    });\n  };\n  const onSubmitForm = async e => {\n    e.preventDefault();\n    try {\n      const response = await axios.post('http://localhost:4000/login', {\n        username,\n        password\n      });\n\n      // Store token in localStorage\n      localStorage.setItem('token', response.data.token);\n\n      // Set auth to true\n      setAuth(true);\n    } catch (err) {\n      console.error(err.response.data);\n      setError(err.response.data.message || 'An error occurred');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"form-container fade-in\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"glass-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          marginBottom: '2rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"Welcome Back\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: 'var(--text-secondary)',\n            marginBottom: '0'\n          },\n          children: \"Sign in to your account to continue\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"alert alert-error\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 19\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: onSubmitForm,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"username\",\n            className: \"form-label\",\n            children: \"\\uD83D\\uDC64 Username\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            name: \"username\",\n            placeholder: \"Enter your username\",\n            className: \"form-input\",\n            value: username,\n            onChange: onChange,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"password\",\n            className: \"form-label\",\n            children: \"\\uD83D\\uDD12 Password\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"password\",\n            name: \"password\",\n            placeholder: \"Enter your password\",\n            className: \"form-input\",\n            value: password,\n            onChange: onChange,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"btn btn-primary btn-block\",\n          children: \"Sign In\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          marginTop: '1.5rem'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: 'var(--text-secondary)',\n            marginBottom: '0'\n          },\n          children: [\"Don't have an account?\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/register\",\n            className: \"link\",\n            children: \"Create one here\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 39,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"EN1mKwCeMkJPcYMz+fKHV3eFEEQ=\");\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "Link", "axios", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "setAuth", "_s", "inputs", "setInputs", "username", "password", "error", "setError", "onChange", "e", "target", "name", "value", "onSubmitForm", "preventDefault", "response", "post", "localStorage", "setItem", "data", "token", "err", "console", "message", "className", "children", "style", "textAlign", "marginBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "onSubmit", "htmlFor", "type", "placeholder", "required", "marginTop", "to", "_c", "$RefreshReg$"], "sources": ["D:/University/Semister 6/WE/Theory/ass_4/jwt-auth-app/src/components/Login.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link } from 'react-router-dom';\nimport axios from 'axios';\n\nconst Login = ({ setAuth }) => {\n  const [inputs, setInputs] = useState({\n    username: '',\n    password: ''\n  });\n  const [error, setError] = useState('');\n\n  const { username, password } = inputs;\n\n  const onChange = (e) => {\n    setInputs({ ...inputs, [e.target.name]: e.target.value });\n  };\n\n  const onSubmitForm = async (e) => {\n    e.preventDefault();\n    try {\n      const response = await axios.post('http://localhost:4000/login', {\n        username,\n        password\n      });\n\n      // Store token in localStorage\n      localStorage.setItem('token', response.data.token);\n      \n      // Set auth to true\n      setAuth(true);\n      \n    } catch (err) {\n      console.error(err.response.data);\n      setError(err.response.data.message || 'An error occurred');\n    }\n  };\n\n  return (\n    <div className=\"form-container fade-in\">\n      <div className=\"glass-card\">\n        <div style={{ textAlign: 'center', marginBottom: '2rem' }}>\n          <h1>Welcome Back</h1>\n          <p style={{ color: 'var(--text-secondary)', marginBottom: '0' }}>\n            Sign in to your account to continue\n          </p>\n        </div>\n\n        {error && <div className=\"alert alert-error\">{error}</div>}\n\n        <form onSubmit={onSubmitForm}>\n          <div className=\"form-group\">\n            <label htmlFor=\"username\" className=\"form-label\">\n              👤 Username\n            </label>\n            <input\n              type=\"text\"\n              name=\"username\"\n              placeholder=\"Enter your username\"\n              className=\"form-input\"\n              value={username}\n              onChange={onChange}\n              required\n            />\n          </div>\n\n          <div className=\"form-group\">\n            <label htmlFor=\"password\" className=\"form-label\">\n              🔒 Password\n            </label>\n            <input\n              type=\"password\"\n              name=\"password\"\n              placeholder=\"Enter your password\"\n              className=\"form-input\"\n              value={password}\n              onChange={onChange}\n              required\n            />\n          </div>\n\n          <button type=\"submit\" className=\"btn btn-primary btn-block\">\n            Sign In\n          </button>\n        </form>\n\n        <div style={{ textAlign: 'center', marginTop: '1.5rem' }}>\n          <p style={{ color: 'var(--text-secondary)', marginBottom: '0' }}>\n            Don't have an account?{' '}\n            <Link to=\"/register\" className=\"link\">\n              Create one here\n            </Link>\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Login;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,KAAK,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAC7B,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGT,QAAQ,CAAC;IACnCU,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EAEtC,MAAM;IAAEU,QAAQ;IAAEC;EAAS,CAAC,GAAGH,MAAM;EAErC,MAAMM,QAAQ,GAAIC,CAAC,IAAK;IACtBN,SAAS,CAAC;MAAE,GAAGD,MAAM;MAAE,CAACO,CAAC,CAACC,MAAM,CAACC,IAAI,GAAGF,CAAC,CAACC,MAAM,CAACE;IAAM,CAAC,CAAC;EAC3D,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOJ,CAAC,IAAK;IAChCA,CAAC,CAACK,cAAc,CAAC,CAAC;IAClB,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMnB,KAAK,CAACoB,IAAI,CAAC,6BAA6B,EAAE;QAC/DZ,QAAQ;QACRC;MACF,CAAC,CAAC;;MAEF;MACAY,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEH,QAAQ,CAACI,IAAI,CAACC,KAAK,CAAC;;MAElD;MACApB,OAAO,CAAC,IAAI,CAAC;IAEf,CAAC,CAAC,OAAOqB,GAAG,EAAE;MACZC,OAAO,CAAChB,KAAK,CAACe,GAAG,CAACN,QAAQ,CAACI,IAAI,CAAC;MAChCZ,QAAQ,CAACc,GAAG,CAACN,QAAQ,CAACI,IAAI,CAACI,OAAO,IAAI,mBAAmB,CAAC;IAC5D;EACF,CAAC;EAED,oBACEzB,OAAA;IAAK0B,SAAS,EAAC,wBAAwB;IAAAC,QAAA,eACrC3B,OAAA;MAAK0B,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzB3B,OAAA;QAAK4B,KAAK,EAAE;UAAEC,SAAS,EAAE,QAAQ;UAAEC,YAAY,EAAE;QAAO,CAAE;QAAAH,QAAA,gBACxD3B,OAAA;UAAA2B,QAAA,EAAI;QAAY;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrBlC,OAAA;UAAG4B,KAAK,EAAE;YAAEO,KAAK,EAAE,uBAAuB;YAAEL,YAAY,EAAE;UAAI,CAAE;UAAAH,QAAA,EAAC;QAEjE;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,EAEL1B,KAAK,iBAAIR,OAAA;QAAK0B,SAAS,EAAC,mBAAmB;QAAAC,QAAA,EAAEnB;MAAK;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAE1DlC,OAAA;QAAMoC,QAAQ,EAAErB,YAAa;QAAAY,QAAA,gBAC3B3B,OAAA;UAAK0B,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB3B,OAAA;YAAOqC,OAAO,EAAC,UAAU;YAACX,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAEjD;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRlC,OAAA;YACEsC,IAAI,EAAC,MAAM;YACXzB,IAAI,EAAC,UAAU;YACf0B,WAAW,EAAC,qBAAqB;YACjCb,SAAS,EAAC,YAAY;YACtBZ,KAAK,EAAER,QAAS;YAChBI,QAAQ,EAAEA,QAAS;YACnB8B,QAAQ;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENlC,OAAA;UAAK0B,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB3B,OAAA;YAAOqC,OAAO,EAAC,UAAU;YAACX,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAEjD;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRlC,OAAA;YACEsC,IAAI,EAAC,UAAU;YACfzB,IAAI,EAAC,UAAU;YACf0B,WAAW,EAAC,qBAAqB;YACjCb,SAAS,EAAC,YAAY;YACtBZ,KAAK,EAAEP,QAAS;YAChBG,QAAQ,EAAEA,QAAS;YACnB8B,QAAQ;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENlC,OAAA;UAAQsC,IAAI,EAAC,QAAQ;UAACZ,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EAAC;QAE5D;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEPlC,OAAA;QAAK4B,KAAK,EAAE;UAAEC,SAAS,EAAE,QAAQ;UAAEY,SAAS,EAAE;QAAS,CAAE;QAAAd,QAAA,eACvD3B,OAAA;UAAG4B,KAAK,EAAE;YAAEO,KAAK,EAAE,uBAAuB;YAAEL,YAAY,EAAE;UAAI,CAAE;UAAAH,QAAA,GAAC,wBACzC,EAAC,GAAG,eAC1B3B,OAAA,CAACH,IAAI;YAAC6C,EAAE,EAAC,WAAW;YAAChB,SAAS,EAAC,MAAM;YAAAC,QAAA,EAAC;UAEtC;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC/B,EAAA,CA5FIF,KAAK;AAAA0C,EAAA,GAAL1C,KAAK;AA8FX,eAAeA,KAAK;AAAC,IAAA0C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}