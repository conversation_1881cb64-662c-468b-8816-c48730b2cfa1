{"ast": null, "code": "var _jsxFileName = \"D:\\\\University\\\\Semister 6\\\\WE\\\\Theory\\\\jwt-auth-app\\\\src\\\\components\\\\Navbar.js\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Navbar = ({\n  isAuthenticated,\n  setAuth\n}) => {\n  const logout = e => {\n    e.preventDefault();\n    localStorage.removeItem('token');\n    setAuth(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"nav\", {\n    className: \"navbar navbar-expand-lg navbar-dark bg-dark\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(Link, {\n        className: \"navbar-brand\",\n        to: \"/\",\n        children: \"JWT Auth App\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"navbar-toggler\",\n        type: \"button\",\n        \"data-bs-toggle\": \"collapse\",\n        \"data-bs-target\": \"#navbarNav\",\n        \"aria-controls\": \"navbarNav\",\n        \"aria-expanded\": \"false\",\n        \"aria-label\": \"Toggle navigation\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"navbar-toggler-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"collapse navbar-collapse\",\n        id: \"navbarNav\",\n        children: /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"navbar-nav ms-auto\",\n          children: !isAuthenticated ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"nav-item\",\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                className: \"nav-link\",\n                to: \"/login\",\n                children: \"Login\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 31,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 30,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"nav-item\",\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                className: \"nav-link\",\n                to: \"/register\",\n                children: \"Register\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 34,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 33,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"nav-item\",\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                className: \"nav-link\",\n                to: \"/dashboard\",\n                children: \"Dashboard\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 40,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 39,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"nav-item\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"nav-link btn btn-link\",\n                onClick: logout,\n                children: \"Logout\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 43,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 42,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 5\n  }, this);\n};\n_c = Navbar;\nexport default Navbar;\nvar _c;\n$RefreshReg$(_c, \"Navbar\");", "map": {"version": 3, "names": ["React", "Link", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON><PERSON><PERSON>", "isAuthenticated", "setAuth", "logout", "e", "preventDefault", "localStorage", "removeItem", "className", "children", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "id", "onClick", "_c", "$RefreshReg$"], "sources": ["D:/University/Semister 6/WE/Theory/jwt-auth-app/src/components/Navbar.js"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\n\nconst Navbar = ({ isAuthenticated, setAuth }) => {\n  const logout = (e) => {\n    e.preventDefault();\n    localStorage.removeItem('token');\n    setAuth(false);\n  };\n\n  return (\n    <nav className=\"navbar navbar-expand-lg navbar-dark bg-dark\">\n      <div className=\"container\">\n        <Link className=\"navbar-brand\" to=\"/\">JWT Auth App</Link>\n        <button \n          className=\"navbar-toggler\" \n          type=\"button\" \n          data-bs-toggle=\"collapse\" \n          data-bs-target=\"#navbarNav\" \n          aria-controls=\"navbarNav\" \n          aria-expanded=\"false\" \n          aria-label=\"Toggle navigation\"\n        >\n          <span className=\"navbar-toggler-icon\"></span>\n        </button>\n        <div className=\"collapse navbar-collapse\" id=\"navbarNav\">\n          <ul className=\"navbar-nav ms-auto\">\n            {!isAuthenticated ? (\n              <>\n                <li className=\"nav-item\">\n                  <Link className=\"nav-link\" to=\"/login\">Login</Link>\n                </li>\n                <li className=\"nav-item\">\n                  <Link className=\"nav-link\" to=\"/register\">Register</Link>\n                </li>\n              </>\n            ) : (\n              <>\n                <li className=\"nav-item\">\n                  <Link className=\"nav-link\" to=\"/dashboard\">Dashboard</Link>\n                </li>\n                <li className=\"nav-item\">\n                  <button \n                    className=\"nav-link btn btn-link\" \n                    onClick={logout}\n                  >\n                    Logout\n                  </button>\n                </li>\n              </>\n            )}\n          </ul>\n        </div>\n      </div>\n    </nav>\n  );\n};\n\nexport default Navbar;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExC,MAAMC,MAAM,GAAGA,CAAC;EAAEC,eAAe;EAAEC;AAAQ,CAAC,KAAK;EAC/C,MAAMC,MAAM,GAAIC,CAAC,IAAK;IACpBA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBC,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;IAChCL,OAAO,CAAC,KAAK,CAAC;EAChB,CAAC;EAED,oBACEL,OAAA;IAAKW,SAAS,EAAC,6CAA6C;IAAAC,QAAA,eAC1DZ,OAAA;MAAKW,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBZ,OAAA,CAACF,IAAI;QAACa,SAAS,EAAC,cAAc;QAACE,EAAE,EAAC,GAAG;QAAAD,QAAA,EAAC;MAAY;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACzDjB,OAAA;QACEW,SAAS,EAAC,gBAAgB;QAC1BO,IAAI,EAAC,QAAQ;QACb,kBAAe,UAAU;QACzB,kBAAe,YAAY;QAC3B,iBAAc,WAAW;QACzB,iBAAc,OAAO;QACrB,cAAW,mBAAmB;QAAAN,QAAA,eAE9BZ,OAAA;UAAMW,SAAS,EAAC;QAAqB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC,eACTjB,OAAA;QAAKW,SAAS,EAAC,0BAA0B;QAACQ,EAAE,EAAC,WAAW;QAAAP,QAAA,eACtDZ,OAAA;UAAIW,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAC/B,CAACR,eAAe,gBACfJ,OAAA,CAAAE,SAAA;YAAAU,QAAA,gBACEZ,OAAA;cAAIW,SAAS,EAAC,UAAU;cAAAC,QAAA,eACtBZ,OAAA,CAACF,IAAI;gBAACa,SAAS,EAAC,UAAU;gBAACE,EAAE,EAAC,QAAQ;gBAAAD,QAAA,EAAC;cAAK;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC,eACLjB,OAAA;cAAIW,SAAS,EAAC,UAAU;cAAAC,QAAA,eACtBZ,OAAA,CAACF,IAAI;gBAACa,SAAS,EAAC,UAAU;gBAACE,EAAE,EAAC,WAAW;gBAAAD,QAAA,EAAC;cAAQ;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC;UAAA,eACL,CAAC,gBAEHjB,OAAA,CAAAE,SAAA;YAAAU,QAAA,gBACEZ,OAAA;cAAIW,SAAS,EAAC,UAAU;cAAAC,QAAA,eACtBZ,OAAA,CAACF,IAAI;gBAACa,SAAS,EAAC,UAAU;gBAACE,EAAE,EAAC,YAAY;gBAAAD,QAAA,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC,eACLjB,OAAA;cAAIW,SAAS,EAAC,UAAU;cAAAC,QAAA,eACtBZ,OAAA;gBACEW,SAAS,EAAC,uBAAuB;gBACjCS,OAAO,EAAEd,MAAO;gBAAAM,QAAA,EACjB;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC;UAAA,eACL;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACI,EAAA,GArDIlB,MAAM;AAuDZ,eAAeA,MAAM;AAAC,IAAAkB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}