const express = require('express');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const cors = require('cors');
const bodyParser = require('body-parser');
const authenticateToken = require('./authMiddleware');

const app = express();
app.use(cors());
app.use(bodyParser.json());

const users = []; // In-memory user store
const SECRET_KEY = '22mdswe203';

// Registration
app.post('/register', async (req, res) => {
  const { username, password } = req.body;

  // Check if user already exists
  if (users.find(u => u.username === username)) {
    return res.status(400).json({ message: 'Username already exists' });
  }

  const hashedPassword = await bcrypt.hash(password, 10);
  users.push({ username, password: hashedPassword });
  res.status(201).json({ message: 'User registered successfully' });
});

// Login
app.post('/login', async (req, res) => {
  const { username, password } = req.body;
  const user = users.find(u => u.username === username);
  if (!user || !(await bcrypt.compare(password, user.password))) {
    return res.status(401).json({ message: 'Invalid credentials' });
  }
  const token = jwt.sign({ username }, SECRET_KEY, { expiresIn: '1h' });
  res.json({ token });
});

// Protected route
app.get('/protected', authenticateToken, (req, res) => {
  res.json({
    message: 'This is a protected route',
    user: req.user
  });
});

// Get user profile
app.get('/profile', authenticateToken, (req, res) => {
  const user = users.find(u => u.username === req.user.username);
  if (!user) {
    return res.status(404).json({ message: 'User not found' });
  }

  // Return user info without password
  res.json({
    username: user.username,
    // Add more user info here if needed
  });
});

app.listen(4000, () => console.log('Server running on port 4000'));