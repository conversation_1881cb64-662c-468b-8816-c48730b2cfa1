{"ast": null, "code": "var _jsxFileName = \"D:\\\\University\\\\Semister 6\\\\WE\\\\Theory\\\\jwt-auth-app\\\\src\\\\components\\\\Login.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link } from 'react-router-dom';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Login = ({\n  setAuth\n}) => {\n  _s();\n  const [inputs, setInputs] = useState({\n    username: '',\n    password: ''\n  });\n  const [error, setError] = useState('');\n  const {\n    username,\n    password\n  } = inputs;\n  const onChange = e => {\n    setInputs({\n      ...inputs,\n      [e.target.name]: e.target.value\n    });\n  };\n  const onSubmitForm = async e => {\n    e.preventDefault();\n    try {\n      const response = await axios.post('http://localhost:4000/login', {\n        username,\n        password\n      });\n\n      // Store token in localStorage\n      localStorage.setItem('token', response.data.token);\n\n      // Set auth to true\n      setAuth(true);\n    } catch (err) {\n      console.error(err.response.data);\n      setError(err.response.data.message || 'An error occurred');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"text-center my-5\",\n      children: \"Login\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"alert alert-danger\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: onSubmitForm,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"username\",\n          className: \"form-label\",\n          children: \"Username\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          name: \"username\",\n          placeholder: \"Username\",\n          className: \"form-control\",\n          value: username,\n          onChange: onChange,\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"password\",\n          className: \"form-label\",\n          children: \"Password\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"password\",\n          name: \"password\",\n          placeholder: \"Password\",\n          className: \"form-control\",\n          value: password,\n          onChange: onChange,\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-primary btn-block\",\n        children: \"Login\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"mt-3\",\n      children: [\"Don't have an account? \", /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/register\",\n        children: \"Register\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 32\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 39,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"EN1mKwCeMkJPcYMz+fKHV3eFEEQ=\");\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "Link", "axios", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "setAuth", "_s", "inputs", "setInputs", "username", "password", "error", "setError", "onChange", "e", "target", "name", "value", "onSubmitForm", "preventDefault", "response", "post", "localStorage", "setItem", "data", "token", "err", "console", "message", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "htmlFor", "type", "placeholder", "required", "to", "_c", "$RefreshReg$"], "sources": ["D:/University/Semister 6/WE/Theory/jwt-auth-app/src/components/Login.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link } from 'react-router-dom';\nimport axios from 'axios';\n\nconst Login = ({ setAuth }) => {\n  const [inputs, setInputs] = useState({\n    username: '',\n    password: ''\n  });\n  const [error, setError] = useState('');\n\n  const { username, password } = inputs;\n\n  const onChange = (e) => {\n    setInputs({ ...inputs, [e.target.name]: e.target.value });\n  };\n\n  const onSubmitForm = async (e) => {\n    e.preventDefault();\n    try {\n      const response = await axios.post('http://localhost:4000/login', {\n        username,\n        password\n      });\n\n      // Store token in localStorage\n      localStorage.setItem('token', response.data.token);\n      \n      // Set auth to true\n      setAuth(true);\n      \n    } catch (err) {\n      console.error(err.response.data);\n      setError(err.response.data.message || 'An error occurred');\n    }\n  };\n\n  return (\n    <div className=\"container\">\n      <h1 className=\"text-center my-5\">Login</h1>\n      {error && <div className=\"alert alert-danger\">{error}</div>}\n      <form onSubmit={onSubmitForm}>\n        <div className=\"mb-3\">\n          <label htmlFor=\"username\" className=\"form-label\">Username</label>\n          <input\n            type=\"text\"\n            name=\"username\"\n            placeholder=\"Username\"\n            className=\"form-control\"\n            value={username}\n            onChange={onChange}\n            required\n          />\n        </div>\n        <div className=\"mb-3\">\n          <label htmlFor=\"password\" className=\"form-label\">Password</label>\n          <input\n            type=\"password\"\n            name=\"password\"\n            placeholder=\"Password\"\n            className=\"form-control\"\n            value={password}\n            onChange={onChange}\n            required\n          />\n        </div>\n        <button className=\"btn btn-primary btn-block\">Login</button>\n      </form>\n      <p className=\"mt-3\">\n        Don't have an account? <Link to=\"/register\">Register</Link>\n      </p>\n    </div>\n  );\n};\n\nexport default Login;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,KAAK,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAC7B,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGT,QAAQ,CAAC;IACnCU,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EAEtC,MAAM;IAAEU,QAAQ;IAAEC;EAAS,CAAC,GAAGH,MAAM;EAErC,MAAMM,QAAQ,GAAIC,CAAC,IAAK;IACtBN,SAAS,CAAC;MAAE,GAAGD,MAAM;MAAE,CAACO,CAAC,CAACC,MAAM,CAACC,IAAI,GAAGF,CAAC,CAACC,MAAM,CAACE;IAAM,CAAC,CAAC;EAC3D,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOJ,CAAC,IAAK;IAChCA,CAAC,CAACK,cAAc,CAAC,CAAC;IAClB,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMnB,KAAK,CAACoB,IAAI,CAAC,6BAA6B,EAAE;QAC/DZ,QAAQ;QACRC;MACF,CAAC,CAAC;;MAEF;MACAY,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEH,QAAQ,CAACI,IAAI,CAACC,KAAK,CAAC;;MAElD;MACApB,OAAO,CAAC,IAAI,CAAC;IAEf,CAAC,CAAC,OAAOqB,GAAG,EAAE;MACZC,OAAO,CAAChB,KAAK,CAACe,GAAG,CAACN,QAAQ,CAACI,IAAI,CAAC;MAChCZ,QAAQ,CAACc,GAAG,CAACN,QAAQ,CAACI,IAAI,CAACI,OAAO,IAAI,mBAAmB,CAAC;IAC5D;EACF,CAAC;EAED,oBACEzB,OAAA;IAAK0B,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxB3B,OAAA;MAAI0B,SAAS,EAAC,kBAAkB;MAAAC,QAAA,EAAC;IAAK;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EAC1CvB,KAAK,iBAAIR,OAAA;MAAK0B,SAAS,EAAC,oBAAoB;MAAAC,QAAA,EAAEnB;IAAK;MAAAoB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAC3D/B,OAAA;MAAMgC,QAAQ,EAAEjB,YAAa;MAAAY,QAAA,gBAC3B3B,OAAA;QAAK0B,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnB3B,OAAA;UAAOiC,OAAO,EAAC,UAAU;UAACP,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACjE/B,OAAA;UACEkC,IAAI,EAAC,MAAM;UACXrB,IAAI,EAAC,UAAU;UACfsB,WAAW,EAAC,UAAU;UACtBT,SAAS,EAAC,cAAc;UACxBZ,KAAK,EAAER,QAAS;UAChBI,QAAQ,EAAEA,QAAS;UACnB0B,QAAQ;QAAA;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACN/B,OAAA;QAAK0B,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnB3B,OAAA;UAAOiC,OAAO,EAAC,UAAU;UAACP,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACjE/B,OAAA;UACEkC,IAAI,EAAC,UAAU;UACfrB,IAAI,EAAC,UAAU;UACfsB,WAAW,EAAC,UAAU;UACtBT,SAAS,EAAC,cAAc;UACxBZ,KAAK,EAAEP,QAAS;UAChBG,QAAQ,EAAEA,QAAS;UACnB0B,QAAQ;QAAA;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACN/B,OAAA;QAAQ0B,SAAS,EAAC,2BAA2B;QAAAC,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxD,CAAC,eACP/B,OAAA;MAAG0B,SAAS,EAAC,MAAM;MAAAC,QAAA,GAAC,yBACK,eAAA3B,OAAA,CAACH,IAAI;QAACwC,EAAE,EAAC,WAAW;QAAAV,QAAA,EAAC;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1D,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEV,CAAC;AAAC5B,EAAA,CArEIF,KAAK;AAAAqC,EAAA,GAALrC,KAAK;AAuEX,eAAeA,KAAK;AAAC,IAAAqC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}