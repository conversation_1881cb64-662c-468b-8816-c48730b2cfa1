"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib @typescript-eslint/scope-manager
Object.defineProperty(exports, "__esModule", { value: true });
exports.es2019_full = void 0;
const dom_1 = require("./dom");
const dom_iterable_1 = require("./dom.iterable");
const es2019_1 = require("./es2019");
const scripthost_1 = require("./scripthost");
const webworker_importscripts_1 = require("./webworker.importscripts");
exports.es2019_full = Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({}, es2019_1.es2019), dom_1.dom), webworker_importscripts_1.webworker_importscripts), scripthost_1.scripthost), dom_iterable_1.dom_iterable);
//# sourceMappingURL=es2019.full.js.map