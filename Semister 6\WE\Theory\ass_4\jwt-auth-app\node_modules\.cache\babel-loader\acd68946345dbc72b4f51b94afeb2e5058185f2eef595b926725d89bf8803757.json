{"ast": null, "code": "var _jsxFileName = \"D:\\\\University\\\\Semister 6\\\\WE\\\\Theory\\\\ass_4\\\\jwt-auth-app\\\\src\\\\components\\\\Register.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link } from 'react-router-dom';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Register = ({\n  setAuth\n}) => {\n  _s();\n  const [inputs, setInputs] = useState({\n    username: '',\n    password: ''\n  });\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const {\n    username,\n    password\n  } = inputs;\n  const onChange = e => {\n    setInputs({\n      ...inputs,\n      [e.target.name]: e.target.value\n    });\n  };\n  const onSubmitForm = async e => {\n    e.preventDefault();\n    try {\n      const response = await axios.post('http://localhost:4000/register', {\n        username,\n        password\n      });\n      setSuccess(response.data.message);\n      setError('');\n\n      // Clear form\n      setInputs({\n        username: '',\n        password: ''\n      });\n    } catch (err) {\n      console.error(err.response.data);\n      setError(err.response.data.message || 'An error occurred');\n      setSuccess('');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"form-container fade-in\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"glass-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          marginBottom: '2rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"Join Us Today\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: 'var(--text-secondary)',\n            marginBottom: '0'\n          },\n          children: \"Create your account to get started\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"alert alert-error\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 19\n      }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"alert alert-success\",\n        children: success\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: onSubmitForm,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"username\",\n            className: \"form-label\",\n            children: \"\\uD83D\\uDC64 Username\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            name: \"username\",\n            placeholder: \"Choose a username\",\n            className: \"form-input\",\n            value: username,\n            onChange: onChange,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"password\",\n            className: \"form-label\",\n            children: \"\\uD83D\\uDD12 Password\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"password\",\n            name: \"password\",\n            placeholder: \"Create a secure password\",\n            className: \"form-input\",\n            value: password,\n            onChange: onChange,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"btn btn-secondary btn-block\",\n          children: \"Create Account\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          marginTop: '1.5rem'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: 'var(--text-secondary)',\n            marginBottom: '0'\n          },\n          children: [\"Already have an account?\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/login\",\n            className: \"link\",\n            children: \"Sign in here\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 44,\n    columnNumber: 5\n  }, this);\n};\n_s(Register, \"TsIsxJUDUyKC6DUgdrVQi+PuvL0=\");\n_c = Register;\nexport default Register;\nvar _c;\n$RefreshReg$(_c, \"Register\");", "map": {"version": 3, "names": ["React", "useState", "Link", "axios", "jsxDEV", "_jsxDEV", "Register", "setAuth", "_s", "inputs", "setInputs", "username", "password", "error", "setError", "success", "setSuccess", "onChange", "e", "target", "name", "value", "onSubmitForm", "preventDefault", "response", "post", "data", "message", "err", "console", "className", "children", "style", "textAlign", "marginBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "onSubmit", "htmlFor", "type", "placeholder", "required", "marginTop", "to", "_c", "$RefreshReg$"], "sources": ["D:/University/Semister 6/WE/Theory/ass_4/jwt-auth-app/src/components/Register.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link } from 'react-router-dom';\nimport axios from 'axios';\n\nconst Register = ({ setAuth }) => {\n  const [inputs, setInputs] = useState({\n    username: '',\n    password: ''\n  });\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n\n  const { username, password } = inputs;\n\n  const onChange = (e) => {\n    setInputs({ ...inputs, [e.target.name]: e.target.value });\n  };\n\n  const onSubmitForm = async (e) => {\n    e.preventDefault();\n    try {\n      const response = await axios.post('http://localhost:4000/register', {\n        username,\n        password\n      });\n\n      setSuccess(response.data.message);\n      setError('');\n      \n      // Clear form\n      setInputs({\n        username: '',\n        password: ''\n      });\n      \n    } catch (err) {\n      console.error(err.response.data);\n      setError(err.response.data.message || 'An error occurred');\n      setSuccess('');\n    }\n  };\n\n  return (\n    <div className=\"form-container fade-in\">\n      <div className=\"glass-card\">\n        <div style={{ textAlign: 'center', marginBottom: '2rem' }}>\n          <h1>Join Us Today</h1>\n          <p style={{ color: 'var(--text-secondary)', marginBottom: '0' }}>\n            Create your account to get started\n          </p>\n        </div>\n\n        {error && <div className=\"alert alert-error\">{error}</div>}\n        {success && <div className=\"alert alert-success\">{success}</div>}\n\n        <form onSubmit={onSubmitForm}>\n          <div className=\"form-group\">\n            <label htmlFor=\"username\" className=\"form-label\">\n              👤 Username\n            </label>\n            <input\n              type=\"text\"\n              name=\"username\"\n              placeholder=\"Choose a username\"\n              className=\"form-input\"\n              value={username}\n              onChange={onChange}\n              required\n            />\n          </div>\n\n          <div className=\"form-group\">\n            <label htmlFor=\"password\" className=\"form-label\">\n              🔒 Password\n            </label>\n            <input\n              type=\"password\"\n              name=\"password\"\n              placeholder=\"Create a secure password\"\n              className=\"form-input\"\n              value={password}\n              onChange={onChange}\n              required\n            />\n          </div>\n\n          <button type=\"submit\" className=\"btn btn-secondary btn-block\">\n            Create Account\n          </button>\n        </form>\n\n        <div style={{ textAlign: 'center', marginTop: '1.5rem' }}>\n          <p style={{ color: 'var(--text-secondary)', marginBottom: '0' }}>\n            Already have an account?{' '}\n            <Link to=\"/login\" className=\"link\">\n              Sign in here\n            </Link>\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Register;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,QAAQ,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAChC,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGT,QAAQ,CAAC;IACnCU,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAE1C,MAAM;IAAEU,QAAQ;IAAEC;EAAS,CAAC,GAAGH,MAAM;EAErC,MAAMQ,QAAQ,GAAIC,CAAC,IAAK;IACtBR,SAAS,CAAC;MAAE,GAAGD,MAAM;MAAE,CAACS,CAAC,CAACC,MAAM,CAACC,IAAI,GAAGF,CAAC,CAACC,MAAM,CAACE;IAAM,CAAC,CAAC;EAC3D,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOJ,CAAC,IAAK;IAChCA,CAAC,CAACK,cAAc,CAAC,CAAC;IAClB,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMrB,KAAK,CAACsB,IAAI,CAAC,gCAAgC,EAAE;QAClEd,QAAQ;QACRC;MACF,CAAC,CAAC;MAEFI,UAAU,CAACQ,QAAQ,CAACE,IAAI,CAACC,OAAO,CAAC;MACjCb,QAAQ,CAAC,EAAE,CAAC;;MAEZ;MACAJ,SAAS,CAAC;QACRC,QAAQ,EAAE,EAAE;QACZC,QAAQ,EAAE;MACZ,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAOgB,GAAG,EAAE;MACZC,OAAO,CAAChB,KAAK,CAACe,GAAG,CAACJ,QAAQ,CAACE,IAAI,CAAC;MAChCZ,QAAQ,CAACc,GAAG,CAACJ,QAAQ,CAACE,IAAI,CAACC,OAAO,IAAI,mBAAmB,CAAC;MAC1DX,UAAU,CAAC,EAAE,CAAC;IAChB;EACF,CAAC;EAED,oBACEX,OAAA;IAAKyB,SAAS,EAAC,wBAAwB;IAAAC,QAAA,eACrC1B,OAAA;MAAKyB,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzB1B,OAAA;QAAK2B,KAAK,EAAE;UAAEC,SAAS,EAAE,QAAQ;UAAEC,YAAY,EAAE;QAAO,CAAE;QAAAH,QAAA,gBACxD1B,OAAA;UAAA0B,QAAA,EAAI;QAAa;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtBjC,OAAA;UAAG2B,KAAK,EAAE;YAAEO,KAAK,EAAE,uBAAuB;YAAEL,YAAY,EAAE;UAAI,CAAE;UAAAH,QAAA,EAAC;QAEjE;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,EAELzB,KAAK,iBAAIR,OAAA;QAAKyB,SAAS,EAAC,mBAAmB;QAAAC,QAAA,EAAElB;MAAK;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,EACzDvB,OAAO,iBAAIV,OAAA;QAAKyB,SAAS,EAAC,qBAAqB;QAAAC,QAAA,EAAEhB;MAAO;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAEhEjC,OAAA;QAAMmC,QAAQ,EAAElB,YAAa;QAAAS,QAAA,gBAC3B1B,OAAA;UAAKyB,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB1B,OAAA;YAAOoC,OAAO,EAAC,UAAU;YAACX,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAEjD;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRjC,OAAA;YACEqC,IAAI,EAAC,MAAM;YACXtB,IAAI,EAAC,UAAU;YACfuB,WAAW,EAAC,mBAAmB;YAC/Bb,SAAS,EAAC,YAAY;YACtBT,KAAK,EAAEV,QAAS;YAChBM,QAAQ,EAAEA,QAAS;YACnB2B,QAAQ;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENjC,OAAA;UAAKyB,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB1B,OAAA;YAAOoC,OAAO,EAAC,UAAU;YAACX,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAEjD;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRjC,OAAA;YACEqC,IAAI,EAAC,UAAU;YACftB,IAAI,EAAC,UAAU;YACfuB,WAAW,EAAC,0BAA0B;YACtCb,SAAS,EAAC,YAAY;YACtBT,KAAK,EAAET,QAAS;YAChBK,QAAQ,EAAEA,QAAS;YACnB2B,QAAQ;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENjC,OAAA;UAAQqC,IAAI,EAAC,QAAQ;UAACZ,SAAS,EAAC,6BAA6B;UAAAC,QAAA,EAAC;QAE9D;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEPjC,OAAA;QAAK2B,KAAK,EAAE;UAAEC,SAAS,EAAE,QAAQ;UAAEY,SAAS,EAAE;QAAS,CAAE;QAAAd,QAAA,eACvD1B,OAAA;UAAG2B,KAAK,EAAE;YAAEO,KAAK,EAAE,uBAAuB;YAAEL,YAAY,EAAE;UAAI,CAAE;UAAAH,QAAA,GAAC,0BACvC,EAAC,GAAG,eAC5B1B,OAAA,CAACH,IAAI;YAAC4C,EAAE,EAAC,QAAQ;YAAChB,SAAS,EAAC,MAAM;YAAAC,QAAA,EAAC;UAEnC;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC9B,EAAA,CAlGIF,QAAQ;AAAAyC,EAAA,GAARzC,QAAQ;AAoGd,eAAeA,QAAQ;AAAC,IAAAyC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}