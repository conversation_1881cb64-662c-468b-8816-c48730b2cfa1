---
description: 'Disallow literal numbers that lose precision.'
---

> 🛑 This file is source code, not the primary documentation location! 🛑
>
> See **https://typescript-eslint.io/rules/no-loss-of-precision** for documentation.

## Examples

This rule extends the base [`eslint/no-loss-of-precision`](https://eslint.org/docs/rules/no-loss-of-precision) rule.
It adds support for [numeric separators](https://github.com/tc39/proposal-numeric-separator).
