{"version": 3, "file": "no-non-null-asserted-optional-chain.js", "sourceRoot": "", "sources": ["../../src/rules/no-non-null-asserted-optional-chain.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAC1D,+CAAiC;AACjC,+CAAiC;AAEjC,8CAAgC;AAEhC,MAAM,OAAO,GAAG,MAAM,CAAC,SAAS,CAC9B,EAAE,CAAC,OAAO,EACV,0CAA0C,EAC1C;IACE,iBAAiB,EAAE,IAAI;CACxB,CACF,CAAC;AAEF,kBAAe,IAAI,CAAC,UAAU,CAAC;IAC7B,IAAI,EAAE,qCAAqC;IAC3C,IAAI,EAAE;QACJ,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EACT,iEAAiE;YACnE,WAAW,EAAE,OAAO;SACrB;QACD,cAAc,EAAE,IAAI;QACpB,QAAQ,EAAE;YACR,sBAAsB,EACpB,6GAA6G;YAC/G,sBAAsB,EAAE,2CAA2C;SACpE;QACD,MAAM,EAAE,EAAE;KACX;IACD,cAAc,EAAE,EAAE;IAClB,MAAM,CAAC,OAAO;QACZ,2EAA2E;QAC3E,qGAAqG;QACrG,2HAA2H;QAC3H,+CAA+C;QAC/C,EAAE;QACF,+CAA+C;QAC/C,YAAY;QACZ,cAAc;QAEd,MAAM,aAAa,GAAG;YACpB,2EAA2E;YAC3E,UAAU;YACV,WAAW;YACX,uCAAuC,CACrC,IAA8B;gBAE9B,qCAAqC;gBACrC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAsC,CAAC;gBAC3D,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,SAAS,EAAE,wBAAwB;oBACnC,oFAAoF;oBACpF,OAAO,EAAE;wBACP;4BACE,SAAS,EAAE,wBAAwB;4BACnC,GAAG,CAAC,KAAK;gCACP,OAAO,KAAK,CAAC,WAAW,CAAC;oCACvB,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;oCACnB,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;iCAChB,CAAC,CAAC;4BACL,CAAC;yBACF;qBACF;iBACF,CAAC,CAAC;YACL,CAAC;YAED,iFAAiF;YACjF,QAAQ;YACR,SAAS;YACT,uCAAuC,CACrC,IAAkC;gBAElC,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,SAAS,EAAE,wBAAwB;oBACnC,oFAAoF;oBACpF,OAAO,EAAE;wBACP;4BACE,SAAS,EAAE,wBAAwB;4BACnC,GAAG,CAAC,KAAK;gCACP,OAAO,KAAK,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;4BAC/D,CAAC;yBACF;qBACF;iBACF,CAAC,CAAC;YACL,CAAC;SACF,CAAC;QAEF,IAAI,OAAO,EAAE;YACX,OAAO,aAAa,CAAC;SACtB;QAED,uCACK,aAAa,KAChB,CAAC;gBACC,8EAA8E;gBAC9E,uEAAuE;gBACvE,qEAAqE;aACtE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,KAAoB;gBAChC,qCAAqC;gBACrC,MAAM,IAAI,GAAG,KAAK,CAAC,MAAsC,CAAC;gBAE1D,IAAI,OAAO,GAAG,KAAK,CAAC;gBACpB,OAAO,OAAO,EAAE;oBACd,QAAQ,OAAO,CAAC,IAAI,EAAE;wBACpB,KAAK,sBAAc,CAAC,gBAAgB;4BAClC,IAAI,OAAO,CAAC,QAAQ,EAAE;gCACpB,2CAA2C;gCAC3C,MAAM;6BACP;4BAED,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC;4BACzB,SAAS;wBAEX,KAAK,sBAAc,CAAC,cAAc;4BAChC,IAAI,OAAO,CAAC,QAAQ,EAAE;gCACpB,2CAA2C;gCAC3C,MAAM;6BACP;4BAED,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC;4BACzB,SAAS;wBAEX;4BACE,kGAAkG;4BAClG,OAAO;qBACV;iBACF;gBAED,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,SAAS,EAAE,wBAAwB;oBACnC,oFAAoF;oBACpF,OAAO,EAAE;wBACP;4BACE,SAAS,EAAE,wBAAwB;4BACnC,GAAG,CAAC,KAAK;gCACP,OAAO,KAAK,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;4BAC/D,CAAC;yBACF;qBACF;iBACF,CAAC,CAAC;YACL,CAAC,IACD;IACJ,CAAC;CACF,CAAC,CAAC"}