{"version": 3, "file": "FeedHandler.d.ts", "sourceRoot": "", "sources": ["../src/FeedHandler.ts"], "names": [], "mappings": "AAAA,OAAO,UAAU,EAAE,EAAE,iBAAiB,EAAiB,MAAM,YAAY,CAAC;AAE1E,OAAO,EAAU,aAAa,EAAE,MAAM,UAAU,CAAC;AAEjD,aAAK,mBAAmB;IACpB,KAAK,IAAA;IACL,KAAK,IAAA;IACL,KAAK,IAAA;IACL,QAAQ,IAAA;IACR,UAAU,IAAA;CACb;AAED,aAAK,uBAAuB;IACxB,MAAM,IAAA;IACN,IAAI,IAAA;IACJ,OAAO,IAAA;CACV;AAED,UAAU,aAAa;IACnB,GAAG,CAAC,EAAE,MAAM,CAAC;IACb,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,MAAM,EAAE,mBAAmB,GAAG,SAAS,CAAC;IACxC,SAAS,EAAE,OAAO,CAAC;IACnB,UAAU,CAAC,EAAE,uBAAuB,CAAC;IACrC,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,IAAI,CAAC,EAAE,MAAM,CAAC;CACjB;AAED,UAAU,QAAQ;IACd,EAAE,CAAC,EAAE,MAAM,CAAC;IACZ,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,OAAO,CAAC,EAAE,IAAI,CAAC;IACf,KAAK,CAAC,EAAE,aAAa,EAAE,CAAC;CAC3B;AAED,UAAU,IAAI;IACV,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,EAAE,CAAC,EAAE,MAAM,CAAC;IACZ,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,OAAO,CAAC,EAAE,IAAI,CAAC;IACf,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,KAAK,CAAC,EAAE,QAAQ,EAAE,CAAC;CACtB;AAGD,qBAAa,WAAY,SAAQ,UAAU;IACvC,IAAI,CAAC,EAAE,IAAI,CAAC;IAEZ;;;;OAIG;gBAEC,QAAQ,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI,KAAK,IAAI,CAAC,GAAG,iBAAiB,EAC9D,OAAO,CAAC,EAAE,iBAAiB;IAS/B,KAAK,IAAI,IAAI;CAkGhB;AA4FD;;;;;GAKG;AACH,wBAAgB,SAAS,CACrB,IAAI,EAAE,MAAM,EACZ,OAAO,GAAE,aAAa,GAAG,iBAAqC,GAC/D,IAAI,GAAG,SAAS,CAIlB"}