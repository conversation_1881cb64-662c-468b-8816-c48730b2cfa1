{"version": 3, "file": "prefer-ts-expect-error.js", "sourceRoot": "", "sources": ["../../src/rules/prefer-ts-expect-error.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA2D;AAM3D,8CAAgC;AAIhC,kBAAe,IAAI,CAAC,UAAU,CAAiB;IAC7C,IAAI,EAAE,wBAAwB;IAC9B,IAAI,EAAE;QACJ,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EAAE,oDAAoD;YACjE,WAAW,EAAE,QAAQ;SACtB;QACD,OAAO,EAAE,MAAM;QACf,QAAQ,EAAE;YACR,wBAAwB,EACtB,yEAAyE;SAC5E;QACD,MAAM,EAAE,EAAE;KACX;IACD,cAAc,EAAE,EAAE;IAClB,MAAM,CAAC,OAAO;QACZ,MAAM,wBAAwB,GAAG,sBAAsB,CAAC;QACxD,MAAM,uBAAuB,GAAG,6BAA6B,CAAC;QAC9D,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;QAE3C,SAAS,aAAa,CAAC,OAAyB;YAC9C,OAAO,OAAO,CAAC,IAAI,KAAK,uBAAe,CAAC,IAAI,CAAC;QAC/C,CAAC;QAED,SAAS,kBAAkB,CAAC,OAAyB;YACnD,IAAI,aAAa,CAAC,OAAO,CAAC,EAAE;gBAC1B,OAAO,OAAO,CAAC,KAAK,CAAC;aACtB;YAED,0DAA0D;YAC1D,MAAM,YAAY,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAC/C,OAAO,YAAY,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAC/C,CAAC;QAED,SAAS,sBAAsB,CAAC,OAAyB;YACvD,MAAM,IAAI,GAAG,kBAAkB,CAAC,OAAO,CAAC,CAAC;YACzC,OAAO,aAAa,CAAC,OAAO,CAAC;gBAC3B,CAAC,CAAC,wBAAwB,CAAC,IAAI,CAAC,IAAI,CAAC;gBACrC,CAAC,CAAC,uBAAuB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzC,CAAC;QAED,OAAO;YACL,OAAO;gBACL,MAAM,QAAQ,GAAG,UAAU,CAAC,cAAc,EAAE,CAAC;gBAC7C,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;oBACzB,IAAI,sBAAsB,CAAC,OAAO,CAAC,EAAE;wBACnC,MAAM,oBAAoB,GAAG,CAAC,KAAgB,EAAW,EAAE,CACzD,KAAK,CAAC,WAAW,CACf,OAAO,EACP,KAAK,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,EAAE,kBAAkB,CAAC,EAAE,CAC/D,CAAC;wBAEJ,MAAM,qBAAqB,GAAG,CAAC,KAAgB,EAAW,EAAE,CAC1D,KAAK,CAAC,WAAW,CACf,OAAO,EACP,KAAK,OAAO,CAAC,KAAK,CAAC,OAAO,CACxB,YAAY,EACZ,kBAAkB,CACnB,IAAI,CACN,CAAC;wBAEJ,OAAO,CAAC,MAAM,CAAC;4BACb,IAAI,EAAE,OAAO;4BACb,SAAS,EAAE,0BAA0B;4BACrC,GAAG,EAAE,aAAa,CAAC,OAAO,CAAC;gCACzB,CAAC,CAAC,oBAAoB;gCACtB,CAAC,CAAC,qBAAqB;yBAC1B,CAAC,CAAC;qBACJ;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}