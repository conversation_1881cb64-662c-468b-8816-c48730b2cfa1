{"version": 3, "file": "typedef.js", "sourceRoot": "", "sources": ["../../src/rules/typedef.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAE1D,8CAAgC;AAiBhC,kBAAe,IAAI,CAAC,UAAU,CAAwB;IACpD,IAAI,EAAE,SAAS;IACf,IAAI,EAAE;QACJ,IAAI,EAAE;YACJ,WAAW,EAAE,4CAA4C;YACzD,WAAW,EAAE,KAAK;SACnB;QACD,QAAQ,EAAE;YACR,eAAe,EAAE,6BAA6B;YAC9C,oBAAoB,EAAE,8CAA8C;SACrE;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,0DAA+B,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;oBACpD,kDAA2B,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;oBAChD,wEAAsC,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;oBAC3D,4DAAgC,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;oBACrD,wCAAsB,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;oBAC3C,4DAAgC,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;oBACrD,4DAAgC,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;oBACrD,wFAA8C,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;iBACpE;aACF;SACF;QACD,IAAI,EAAE,YAAY;KACnB;IACD,cAAc,EAAE;QACd;YACE,0DAA+B,EAAE,KAAK;YACtC,kDAA2B,EAAE,KAAK;YAClC,wEAAsC,EAAE,KAAK;YAC7C,4DAAgC,EAAE,KAAK;YACvC,wCAAsB,EAAE,KAAK;YAC7B,4DAAgC,EAAE,KAAK;YACvC,4DAAgC,EAAE,KAAK;YACvC,wFAA8C,EAAE,KAAK;SACtD;KACF;IACD,MAAM,CACJ,OAAO,EACP,CACE,EACE,kBAAkB,EAClB,cAAc,EACd,yBAAyB,EACzB,mBAAmB,EACnB,SAAS,EACT,mBAAmB,EACnB,mBAAmB,EACnB,iCAAiC,GAClC,EACF;QAED,SAAS,MAAM,CAAC,QAAuB,EAAE,IAAa;YACpD,OAAO,CAAC,MAAM,CAAC;gBACb,IAAI,EAAE,QAAQ;gBACd,SAAS,EAAE,IAAI,CAAC,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,iBAAiB;gBAC5D,IAAI,EAAE,EAAE,IAAI,EAAE;aACf,CAAC,CAAC;QACL,CAAC;QAED,SAAS,WAAW,CAClB,IAAgD;YAEhD,OAAO,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC;QACzE,CAAC;QAED,SAAS,uBAAuB,CAC9B,IAAoD;YAEpD,IAAI,OAAO,GAA8B,IAAI,CAAC,MAAM,CAAC;YACrD,OAAO,OAAO,EAAE;gBACd,QAAQ,OAAO,CAAC,IAAI,EAAE;oBACpB,KAAK,sBAAc,CAAC,kBAAkB,CAAC;oBACvC,KAAK,sBAAc,CAAC,mBAAmB,CAAC;oBACxC,KAAK,sBAAc,CAAC,aAAa,CAAC;oBAClC,KAAK,sBAAc,CAAC,YAAY,CAAC;oBACjC,KAAK,sBAAc,CAAC,QAAQ;wBAC1B,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC;wBACzB,MAAM;oBAER,KAAK,sBAAc,CAAC,cAAc;wBAChC,OAAO,IAAI,CAAC;oBAEd;wBACE,OAAO,GAAG,SAAS,CAAC;iBACvB;aACF;YAED,OAAO,KAAK,CAAC;QACf,CAAC;QAED,SAAS,eAAe,CAAC,MAA4B;YACnD,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;gBAC1B,IAAI,cAAyC,CAAC;gBAE9C,QAAQ,KAAK,CAAC,IAAI,EAAE;oBAClB,KAAK,sBAAc,CAAC,iBAAiB;wBACnC,cAAc,GAAG,KAAK,CAAC,IAAI,CAAC;wBAC5B,MAAM;oBACR,KAAK,sBAAc,CAAC,mBAAmB;wBACrC,cAAc,GAAG,KAAK,CAAC,SAAS,CAAC;wBAEjC,4GAA4G;wBAC5G,IACE,cAAc;4BACd,cAAc,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB,EACxD;4BACA,cAAc,GAAG,cAAc,CAAC,IAAI,CAAC;yBACtC;wBAED,MAAM;oBACR;wBACE,cAAc,GAAG,KAAK,CAAC;wBACvB,MAAM;iBACT;gBAED,IAAI,cAAc,KAAK,SAAS,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE;oBAClE,MAAM,CAAC,KAAK,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC;iBACnC;aACF;QACH,CAAC;QAED,SAAS,mCAAmC,CAAC,IAAmB;YAC9D,OAAO,CACL,iCAAiC,KAAK,IAAI;gBAC1C,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,uBAAuB;oBACnD,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB,CAAC,CACnD,CAAC;QACJ,CAAC;QAED,SAAS,2BAA2B,CAClC,IAAoD;YAEpD,IAAI,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC;YAE3B,OAAO,QAAQ,EAAE;gBACf,IACE,CAAC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,aAAa;oBAC7C,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,YAAY,CAAC;oBAChD,QAAQ,CAAC,cAAc,EACvB;oBACA,OAAO,IAAI,CAAC;iBACb;gBAED,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC;aAC5B;YAED,OAAO,KAAK,CAAC;QACf,CAAC;QAED,6GACK,CAAC,kBAAkB,IAAI;YACxB,YAAY,CAAC,IAAI;;gBACf,IACE,CAAA,MAAA,IAAI,CAAC,MAAM,0CAAE,IAAI,MAAK,sBAAc,CAAC,WAAW;oBAChD,IAAI,CAAC,MAAM,CAAC,cAAc,EAC1B;oBACA,OAAO;iBACR;gBAED,IACE,CAAC,IAAI,CAAC,cAAc;oBACpB,CAAC,uBAAuB,CAAC,IAAI,CAAC;oBAC9B,CAAC,2BAA2B,CAAC,IAAI,CAAC;oBAClC,CAAA,MAAA,IAAI,CAAC,MAAM,0CAAE,IAAI,MAAK,sBAAc,CAAC,oBAAoB,EACzD;oBACA,MAAM,CAAC,IAAI,CAAC,CAAC;iBACd;YACH,CAAC;SACF,CAAC,GACC,CAAC,cAAc,IAAI;YACpB,uBAAuB,CAAC,IAAI;gBAC1B,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC/B,CAAC;SACF,CAAC,GACC,CAAC,yBAAyB,IAAI;YAC/B,kBAAkB,CAAC,IAAI;gBACrB,IACE,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,mCAAmC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;oBAChE,CAAC,IAAI,CAAC,cAAc,EACpB;oBACA,MAAM,CACJ,IAAI,EACJ,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;wBACzC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI;wBACf,CAAC,CAAC,SAAS,CACd,CAAC;iBACH;YACH,CAAC;SACF,CAAC,GACC,CAAC,SAAS,IAAI;YACf,yCAAyC,CACvC,IAAgE;gBAEhE,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC/B,CAAC;SACF,CAAC,GACC,CAAC,mBAAmB,IAAI;YACzB,aAAa,CAAC,IAAI;gBAChB,IACE,CAAC,IAAI,CAAC,cAAc;oBACpB,CAAC,uBAAuB,CAAC,IAAI,CAAC;oBAC9B,CAAC,2BAA2B,CAAC,IAAI,CAAC,EAClC;oBACA,MAAM,CAAC,IAAI,CAAC,CAAC;iBACd;YACH,CAAC;SACF,CAAC,GACC,CAAC,mBAAmB,IAAI;YACzB,uCAAuC,CACrC,IAA8D;gBAE9D,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;oBACxB,MAAM,CACJ,IAAI,EACJ,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,mBAAmB;wBAC9C,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC;wBACvB,CAAC,CAAC,SAAS,CACd,CAAC;iBACH;YACH,CAAC;SACF,CAAC,KACF,kBAAkB,CAAC,IAAI;gBACrB,IACE,CAAC,mBAAmB;oBACpB,IAAI,CAAC,EAAE,CAAC,cAAc;oBACtB,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,sBAAc,CAAC,YAAY;wBAC3C,CAAC,kBAAkB,CAAC;oBACtB,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,sBAAc,CAAC,aAAa;wBAC5C,CAAC,mBAAmB,CAAC;oBACvB,CAAC,IAAI,CAAC,IAAI,IAAI,mCAAmC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAC7D;oBACA,OAAO;iBACR;gBAED,IAAI,OAAO,GAA8B,IAAI,CAAC,MAAM,CAAC;gBACrD,OAAO,OAAO,EAAE;oBACd,QAAQ,OAAO,CAAC,IAAI,EAAE;wBACpB,KAAK,sBAAc,CAAC,mBAAmB;4BACrC,uBAAuB;4BACvB,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC;4BACzB,MAAM;wBACR,KAAK,sBAAc,CAAC,cAAc,CAAC;wBACnC,KAAK,sBAAc,CAAC,cAAc;4BAChC,4CAA4C;4BAC5C,OAAO;wBACT;4BACE,kBAAkB;4BAClB,OAAO,GAAG,SAAS,CAAC;4BACpB,MAAM;qBACT;iBACF;gBAED,MAAM,CAAC,IAAI,EAAE,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;YACrC,CAAC,IACD;IACJ,CAAC;CACF,CAAC,CAAC"}