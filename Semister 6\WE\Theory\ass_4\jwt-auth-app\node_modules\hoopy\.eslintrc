env:
  es6: true
  node: true
rules:
  accessor-pairs: [ 2, { "setWithoutGet": true } ]
  array-bracket-spacing: [ 2, "always" ]
  array-callback-return: 2
  arrow-body-style: 0
  arrow-parens: 0
  arrow-spacing: [ 2, { "before": true, "after": true } ]
  block-scoped-var: 1
  block-spacing: [ 2, "always" ]
  brace-style: [ 2, "1tbs" ]
  callback-return: 0
  camelcase: [ 2, { "properties": "always" } ]
  comma-dangle: 0
  comma-spacing: [ 2, { "before": false, "after": true } ]
  computed-property-spacing: [ 2, "never" ]
  consistent-return: 0
  consistent-this: 0
  constructor-super: 0
  curly: [ 2, "all" ]
  default-case: 0
  dot-location: [ 2, "property" ]
  dot-notation: [ 2, { "allowKeywords": true } ]
  eol-last: 2
  eqeqeq: 2
  func-names: 0
  func-style: [ 2, "declaration" ]
  generator-star-spacing: [ 2, "after" ]
  global-require: 0
  guard-for-in: 2
  handle-callback-err: [ 2, "error" ]
  id-blacklist: 0
  id-length: 0
  id-match: 0
  indent: [ 2, 2, { "SwitchCase": 1 } ]
  init-declarations: 0
  jsx-quotes: 0
  key-spacing: [ 2, { "beforeColon": false, "afterColon": true } ]
  keyword-spacing: [ 2, { "before": true, "after": true } ]
  linebreak-style: [ 2, "unix" ]
  lines-around-comment: 0
  max-depth: [ 1, 4 ]
  max-nested-callbacks: [ 1, 2 ]
  max-params: [ 1, 4 ]
  max-statements: 0
  new-cap: 2
  new-parens: 2
  newline-after-var: 0
  newline-before-return: 0
  newline-per-chained-call: 1
  no-alert: 2
  no-array-constructor: 2
  no-bitwise: 1
  no-caller: 2
  no-case-declarations: 2
  no-catch-shadow: 2
  no-class-assign: 2
  no-cond-assign: [ 2, "always" ]
  no-confusing-arrow: 0
  no-console: 1
  no-constant-condition: 2
  no-const-assign: 2
  no-continue: 0
  no-control-regex: 2
  no-debugger: 2
  no-delete-var: 2
  no-div-regex: 0
  no-dupe-args: 2
  no-dupe-class-members: 2
  no-dupe-keys: 2
  no-duplicate-case: 2
  no-else-return: 2
  no-empty: 2
  no-empty-character-class: 2
  no-empty-function: 0
  no-empty-pattern: 2
  no-eq-null: 2
  no-eval: 2
  no-ex-assign: 2
  no-extend-native: 2
  no-extra-bind: 2
  no-extra-boolean-cast: 2
  no-extra-label: 2
  no-extra-parens: [ 2, "all" ]
  no-extra-semi: 2
  no-fallthrough: 1
  no-floating-decimal: 2
  no-func-assign: 2
  no-implicit-coercion: 0
  no-implicit-globals: 0
  no-implied-eval: 2
  no-inline-comments: 2
  no-inner-declarations: [ 2, "both" ]
  no-invalid-regexp: 2
  no-invalid-this: 0
  no-irregular-whitespace: 2
  no-iterator: 2
  no-labels: 2
  no-label-var: 2
  no-lonely-if: 2
  no-lone-blocks: 1
  no-loop-func: 2
  no-magic-numbers: 0
  no-mixed-requires: 0
  no-mixed-spaces-and-tabs: 2
  no-multiple-empty-lines: [ 2, { "max": 1, "maxEOF": 1, "maxBOF": 0 } ]
  no-multi-spaces: 2
  no-multi-str: 2
  no-native-reassign: 2
  no-negated-condition: 2
  no-negated-in-lhs: 2
  no-nested-ternary: 2
  no-new: 1
  no-new-func: 2
  no-new-object: 2
  no-new-require: 2
  no-new-symbol: 2
  no-new-wrappers: 2
  no-obj-calls: 2
  no-octal: 2
  no-octal-escape: 2
  no-param-reassign: 0
  no-path-concat: 2
  no-plusplus: 0
  no-process-env: 0
  no-process-exit: 1
  no-proto: 2
  no-redeclare: [ 2, { "builtinGlobals": true } ]
  no-regex-spaces: 2
  no-restricted-globals: 2
  no-restricted-imports: 0
  no-restricted-modules: 0
  no-restricted-syntax: 0
  no-return-assign: 0
  no-script-url: 2
  no-self-assign: 2
  no-self-compare: 2
  no-sequences: 2
  no-shadow: [ 2, { "hoist": "all" } ]
  no-shadow-restricted-names: 2
  no-spaced-func: 2
  no-sparse-arrays: 2
  no-sync: 0
  no-ternary: 1
  no-this-before-super: 2
  no-throw-literal: 1
  no-trailing-spaces: 2
  no-undef: 2
  no-undef-init: 2
  no-undefined: 0
  no-underscore-dangle: 2
  no-unexpected-multiline: 2
  no-unmodified-loop-condition: 2
  no-unneeded-ternary: 2
  no-unreachable: 2
  no-unused-expressions: 2
  no-unused-labels: 2
  no-unused-vars: [ 2, "all" ]
  no-useless-call: 2
  no-useless-concat: 2
  no-useless-constructor: 2
  no-use-before-define: 0
  no-var: 1
  no-void: 2
  no-warning-comments: 1
  no-whitespace-before-property: 2
  no-with: 2
  object-curly-spacing: [ 2, "always" ]
  object-shorthand: [ 2, "always" ]
  one-var: 0
  one-var-declaration-per-line: 0
  operator-assignment: [ 2, "always" ]
  operator-linebreak: [ 2, "after" ]
  padded-blocks: [ 2, "never" ]
  prefer-arrow-callback: 2
  prefer-const: 2
  prefer-reflect: 0
  prefer-rest-params: 2
  prefer-spread: 2
  prefer-template: 2
  quote-props: [ 2, "as-needed" ]
  radix: 0
  require-jsdoc: 0
  require-yield: 2
  semi: [ 2, "never" ]
  semi-spacing: 0
  sort-imports: 0
  sort-vars: 0
  space-before-blocks: [ 2, "always" ]
  space-before-function-paren: [ 2, { "anonymous": "always", "named": "always" } ]
  space-infix-ops: 2
  space-unary-ops: 0
  spaced-comment: [ 1, "always" ]
  strict: [ 2, "global" ]
  template-curly-spacing: [ 2, "never" ]
  use-isnan: 2
  valid-jsdoc: 0
  valid-typeof: 2
  vars-on-top: 0
  wrap-iife: 2
  wrap-regex: 0
  yield-star-spacing: [ 2, { "before": false, "after": true } ]
  yoda: [ 2, "never" ]
