{"ast": null, "code": "var _jsxFileName = \"D:\\\\University\\\\Semister 6\\\\WE\\\\Theory\\\\jwt-auth-app\\\\src\\\\components\\\\Dashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = ({\n  setAuth\n}) => {\n  _s();\n  const [name, setName] = useState('');\n  const [protectedData, setProtectedData] = useState('');\n  const [error, setError] = useState('');\n  const getProfile = async () => {\n    try {\n      const token = localStorage.getItem('token');\n      const response = await axios.get('http://localhost:4000/profile', {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      setName(response.data.username);\n    } catch (err) {\n      console.error(err.response);\n      if (err.response.status === 401 || err.response.status === 403) {\n        localStorage.removeItem('token');\n        setAuth(false);\n      }\n      setError('Failed to fetch profile data');\n    }\n  };\n  const getProtectedData = async () => {\n    try {\n      const token = localStorage.getItem('token');\n      const response = await axios.get('http://localhost:4000/protected', {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      setProtectedData(response.data.message);\n    } catch (err) {\n      console.error(err.response);\n      if (err.response.status === 401 || err.response.status === 403) {\n        localStorage.removeItem('token');\n        setAuth(false);\n      }\n      setError('Failed to fetch protected data');\n    }\n  };\n  useEffect(() => {\n    getProfile();\n    getProtectedData();\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"text-center my-5\",\n      children: \"Dashboard\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"alert alert-danger\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-body\",\n        children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n          className: \"card-title\",\n          children: [\"Welcome, \", name, \"!\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"card-text\",\n          children: \"You are now logged in and can access protected resources.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"card-text\",\n          children: [\"Protected data: \", protectedData]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 53,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"LsyAd626PknZGt+vDhHz4tzQGe0=\");\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "jsxDEV", "_jsxDEV", "Dashboard", "setAuth", "_s", "name", "setName", "protectedData", "setProtectedData", "error", "setError", "getProfile", "token", "localStorage", "getItem", "response", "get", "headers", "Authorization", "data", "username", "err", "console", "status", "removeItem", "getProtectedData", "message", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/University/Semister 6/WE/Theory/jwt-auth-app/src/components/Dashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\n\nconst Dashboard = ({ setAuth }) => {\n  const [name, setName] = useState('');\n  const [protectedData, setProtectedData] = useState('');\n  const [error, setError] = useState('');\n\n  const getProfile = async () => {\n    try {\n      const token = localStorage.getItem('token');\n      \n      const response = await axios.get('http://localhost:4000/profile', {\n        headers: { Authorization: `Bearer ${token}` }\n      });\n      \n      setName(response.data.username);\n    } catch (err) {\n      console.error(err.response);\n      if (err.response.status === 401 || err.response.status === 403) {\n        localStorage.removeItem('token');\n        setAuth(false);\n      }\n      setError('Failed to fetch profile data');\n    }\n  };\n\n  const getProtectedData = async () => {\n    try {\n      const token = localStorage.getItem('token');\n      \n      const response = await axios.get('http://localhost:4000/protected', {\n        headers: { Authorization: `Bearer ${token}` }\n      });\n      \n      setProtectedData(response.data.message);\n    } catch (err) {\n      console.error(err.response);\n      if (err.response.status === 401 || err.response.status === 403) {\n        localStorage.removeItem('token');\n        setAuth(false);\n      }\n      setError('Failed to fetch protected data');\n    }\n  };\n\n  useEffect(() => {\n    getProfile();\n    getProtectedData();\n  }, []);\n\n  return (\n    <div className=\"container\">\n      <h1 className=\"text-center my-5\">Dashboard</h1>\n      {error && <div className=\"alert alert-danger\">{error}</div>}\n      <div className=\"card\">\n        <div className=\"card-body\">\n          <h5 className=\"card-title\">Welcome, {name}!</h5>\n          <p className=\"card-text\">You are now logged in and can access protected resources.</p>\n          <p className=\"card-text\">Protected data: {protectedData}</p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,SAAS,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACjC,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACU,aAAa,EAAEC,gBAAgB,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACY,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EAEtC,MAAMc,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAE3C,MAAMC,QAAQ,GAAG,MAAMhB,KAAK,CAACiB,GAAG,CAAC,+BAA+B,EAAE;QAChEC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUN,KAAK;QAAG;MAC9C,CAAC,CAAC;MAEFN,OAAO,CAACS,QAAQ,CAACI,IAAI,CAACC,QAAQ,CAAC;IACjC,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZC,OAAO,CAACb,KAAK,CAACY,GAAG,CAACN,QAAQ,CAAC;MAC3B,IAAIM,GAAG,CAACN,QAAQ,CAACQ,MAAM,KAAK,GAAG,IAAIF,GAAG,CAACN,QAAQ,CAACQ,MAAM,KAAK,GAAG,EAAE;QAC9DV,YAAY,CAACW,UAAU,CAAC,OAAO,CAAC;QAChCrB,OAAO,CAAC,KAAK,CAAC;MAChB;MACAO,QAAQ,CAAC,8BAA8B,CAAC;IAC1C;EACF,CAAC;EAED,MAAMe,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAMb,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAE3C,MAAMC,QAAQ,GAAG,MAAMhB,KAAK,CAACiB,GAAG,CAAC,iCAAiC,EAAE;QAClEC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUN,KAAK;QAAG;MAC9C,CAAC,CAAC;MAEFJ,gBAAgB,CAACO,QAAQ,CAACI,IAAI,CAACO,OAAO,CAAC;IACzC,CAAC,CAAC,OAAOL,GAAG,EAAE;MACZC,OAAO,CAACb,KAAK,CAACY,GAAG,CAACN,QAAQ,CAAC;MAC3B,IAAIM,GAAG,CAACN,QAAQ,CAACQ,MAAM,KAAK,GAAG,IAAIF,GAAG,CAACN,QAAQ,CAACQ,MAAM,KAAK,GAAG,EAAE;QAC9DV,YAAY,CAACW,UAAU,CAAC,OAAO,CAAC;QAChCrB,OAAO,CAAC,KAAK,CAAC;MAChB;MACAO,QAAQ,CAAC,gCAAgC,CAAC;IAC5C;EACF,CAAC;EAEDZ,SAAS,CAAC,MAAM;IACda,UAAU,CAAC,CAAC;IACZc,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EAEN,oBACExB,OAAA;IAAK0B,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxB3B,OAAA;MAAI0B,SAAS,EAAC,kBAAkB;MAAAC,QAAA,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EAC9CvB,KAAK,iBAAIR,OAAA;MAAK0B,SAAS,EAAC,oBAAoB;MAAAC,QAAA,EAAEnB;IAAK;MAAAoB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAC3D/B,OAAA;MAAK0B,SAAS,EAAC,MAAM;MAAAC,QAAA,eACnB3B,OAAA;QAAK0B,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB3B,OAAA;UAAI0B,SAAS,EAAC,YAAY;UAAAC,QAAA,GAAC,WAAS,EAACvB,IAAI,EAAC,GAAC;QAAA;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChD/B,OAAA;UAAG0B,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAyD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACtF/B,OAAA;UAAG0B,SAAS,EAAC,WAAW;UAAAC,QAAA,GAAC,kBAAgB,EAACrB,aAAa;QAAA;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC5B,EAAA,CA7DIF,SAAS;AAAA+B,EAAA,GAAT/B,SAAS;AA+Df,eAAeA,SAAS;AAAC,IAAA+B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}