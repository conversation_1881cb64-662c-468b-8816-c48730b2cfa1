{"ast": null, "code": "var _jsxFileName = \"D:\\\\University\\\\Semister 6\\\\WE\\\\Theory\\\\ass_4\\\\jwt-auth-app\\\\src\\\\components\\\\Navbar.js\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Navbar = ({\n  isAuthenticated,\n  setAuth\n}) => {\n  const logout = e => {\n    e.preventDefault();\n    localStorage.removeItem('token');\n    setAuth(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"nav\", {\n    className: \"navbar\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"navbar-container\",\n      children: [/*#__PURE__*/_jsxDEV(Link, {\n        className: \"navbar-brand\",\n        to: \"/\",\n        children: \"\\u2728 SecureApp\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n        className: \"navbar-nav\",\n        children: !isAuthenticated ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              className: \"nav-link\",\n              to: \"/login\",\n              children: \"\\uD83D\\uDD11 Sign In\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 22,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              className: \"nav-link\",\n              to: \"/register\",\n              children: \"\\uD83D\\uDCDD Sign Up\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 27,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              className: \"nav-link\",\n              to: \"/dashboard\",\n              children: \"\\uD83C\\uDFE0 Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 35,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"nav-link\",\n              onClick: logout,\n              style: {\n                background: 'none',\n                border: 'none',\n                cursor: 'pointer'\n              },\n              children: \"\\uD83D\\uDEAA Logout\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 40,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 5\n  }, this);\n};\n_c = Navbar;\nexport default Navbar;\nvar _c;\n$RefreshReg$(_c, \"Navbar\");", "map": {"version": 3, "names": ["React", "Link", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON><PERSON><PERSON>", "isAuthenticated", "setAuth", "logout", "e", "preventDefault", "localStorage", "removeItem", "className", "children", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "style", "background", "border", "cursor", "_c", "$RefreshReg$"], "sources": ["D:/University/Semister 6/WE/Theory/ass_4/jwt-auth-app/src/components/Navbar.js"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\n\nconst Navbar = ({ isAuthenticated, setAuth }) => {\n  const logout = (e) => {\n    e.preventDefault();\n    localStorage.removeItem('token');\n    setAuth(false);\n  };\n\n  return (\n    <nav className=\"navbar\">\n      <div className=\"navbar-container\">\n        <Link className=\"navbar-brand\" to=\"/\">\n          ✨ SecureApp\n        </Link>\n\n        <ul className=\"navbar-nav\">\n          {!isAuthenticated ? (\n            <>\n              <li>\n                <Link className=\"nav-link\" to=\"/login\">\n                  🔑 Sign In\n                </Link>\n              </li>\n              <li>\n                <Link className=\"nav-link\" to=\"/register\">\n                  📝 Sign Up\n                </Link>\n              </li>\n            </>\n          ) : (\n            <>\n              <li>\n                <Link className=\"nav-link\" to=\"/dashboard\">\n                  🏠 Dashboard\n                </Link>\n              </li>\n              <li>\n                <button\n                  className=\"nav-link\"\n                  onClick={logout}\n                  style={{\n                    background: 'none',\n                    border: 'none',\n                    cursor: 'pointer'\n                  }}\n                >\n                  🚪 Logout\n                </button>\n              </li>\n            </>\n          )}\n        </ul>\n      </div>\n    </nav>\n  );\n};\n\nexport default Navbar;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExC,MAAMC,MAAM,GAAGA,CAAC;EAAEC,eAAe;EAAEC;AAAQ,CAAC,KAAK;EAC/C,MAAMC,MAAM,GAAIC,CAAC,IAAK;IACpBA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBC,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;IAChCL,OAAO,CAAC,KAAK,CAAC;EAChB,CAAC;EAED,oBACEL,OAAA;IAAKW,SAAS,EAAC,QAAQ;IAAAC,QAAA,eACrBZ,OAAA;MAAKW,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BZ,OAAA,CAACF,IAAI;QAACa,SAAS,EAAC,cAAc;QAACE,EAAE,EAAC,GAAG;QAAAD,QAAA,EAAC;MAEtC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAEPjB,OAAA;QAAIW,SAAS,EAAC,YAAY;QAAAC,QAAA,EACvB,CAACR,eAAe,gBACfJ,OAAA,CAAAE,SAAA;UAAAU,QAAA,gBACEZ,OAAA;YAAAY,QAAA,eACEZ,OAAA,CAACF,IAAI;cAACa,SAAS,EAAC,UAAU;cAACE,EAAE,EAAC,QAAQ;cAAAD,QAAA,EAAC;YAEvC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACLjB,OAAA;YAAAY,QAAA,eACEZ,OAAA,CAACF,IAAI;cAACa,SAAS,EAAC,UAAU;cAACE,EAAE,EAAC,WAAW;cAAAD,QAAA,EAAC;YAE1C;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA,eACL,CAAC,gBAEHjB,OAAA,CAAAE,SAAA;UAAAU,QAAA,gBACEZ,OAAA;YAAAY,QAAA,eACEZ,OAAA,CAACF,IAAI;cAACa,SAAS,EAAC,UAAU;cAACE,EAAE,EAAC,YAAY;cAAAD,QAAA,EAAC;YAE3C;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACLjB,OAAA;YAAAY,QAAA,eACEZ,OAAA;cACEW,SAAS,EAAC,UAAU;cACpBO,OAAO,EAAEZ,MAAO;cAChBa,KAAK,EAAE;gBACLC,UAAU,EAAE,MAAM;gBAClBC,MAAM,EAAE,MAAM;gBACdC,MAAM,EAAE;cACV,CAAE;cAAAV,QAAA,EACH;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC;QAAA,eACL;MACH;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACM,EAAA,GAtDIpB,MAAM;AAwDZ,eAAeA,MAAM;AAAC,IAAAoB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}