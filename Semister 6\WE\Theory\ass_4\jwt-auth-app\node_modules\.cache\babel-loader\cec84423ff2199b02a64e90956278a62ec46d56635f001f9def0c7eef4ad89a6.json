{"ast": null, "code": "var _jsxFileName = \"D:\\\\University\\\\Semister 6\\\\WE\\\\Theory\\\\ass_4\\\\jwt-auth-app\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport Login from './components/Login';\nimport Register from './components/Register';\nimport Dashboard from './components/Dashboard';\nimport Navbar from './components/Navbar';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  useEffect(() => {\n    // Check if token exists in localStorage\n    const token = localStorage.getItem('token');\n    if (token) {\n      setIsAuthenticated(true);\n    }\n  }, []);\n  const setAuth = boolean => {\n    setIsAuthenticated(boolean);\n  };\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"app-container\",\n      children: [/*#__PURE__*/_jsxDEV(Navbar, {\n        isAuthenticated: isAuthenticated,\n        setAuth: setAuth\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"main-content\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"container\",\n          children: /*#__PURE__*/_jsxDEV(Routes, {\n            children: [/*#__PURE__*/_jsxDEV(Route, {\n              path: \"/login\",\n              element: !isAuthenticated ? /*#__PURE__*/_jsxDEV(Login, {\n                setAuth: setAuth\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 32,\n                columnNumber: 45\n              }, this) : /*#__PURE__*/_jsxDEV(Navigate, {\n                to: \"/dashboard\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 32,\n                columnNumber: 75\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 30,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/register\",\n              element: !isAuthenticated ? /*#__PURE__*/_jsxDEV(Register, {\n                setAuth: setAuth\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 36,\n                columnNumber: 45\n              }, this) : /*#__PURE__*/_jsxDEV(Navigate, {\n                to: \"/dashboard\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 36,\n                columnNumber: 78\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 34,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/dashboard\",\n              element: isAuthenticated ? /*#__PURE__*/_jsxDEV(Dashboard, {\n                setAuth: setAuth\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 40,\n                columnNumber: 44\n              }, this) : /*#__PURE__*/_jsxDEV(Navigate, {\n                to: \"/login\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 40,\n                columnNumber: 78\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 38,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/\",\n              element: /*#__PURE__*/_jsxDEV(Navigate, {\n                to: isAuthenticated ? \"/dashboard\" : \"/login\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 44,\n                columnNumber: 26\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 42,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 24,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"JkS3Meyzlj18m4l86SBr9YDqEkQ=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "<PERSON><PERSON>", "Register", "Dashboard", "<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "App", "_s", "isAuthenticated", "setIsAuthenticated", "token", "localStorage", "getItem", "setAuth", "boolean", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "to", "_c", "$RefreshReg$"], "sources": ["D:/University/Semister 6/WE/Theory/ass_4/jwt-auth-app/src/App.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport Login from './components/Login';\nimport Register from './components/Register';\nimport Dashboard from './components/Dashboard';\nimport Navbar from './components/Navbar';\n\nfunction App() {\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  \n  useEffect(() => {\n    // Check if token exists in localStorage\n    const token = localStorage.getItem('token');\n    if (token) {\n      setIsAuthenticated(true);\n    }\n  }, []);\n\n  const setAuth = (boolean) => {\n    setIsAuthenticated(boolean);\n  };\n\n  return (\n    <Router>\n      <div className=\"app-container\">\n        <Navbar isAuthenticated={isAuthenticated} setAuth={setAuth} />\n        <main className=\"main-content\">\n          <div className=\"container\">\n            <Routes>\n              <Route\n                path=\"/login\"\n                element={!isAuthenticated ? <Login setAuth={setAuth} /> : <Navigate to=\"/dashboard\" />}\n              />\n              <Route\n                path=\"/register\"\n                element={!isAuthenticated ? <Register setAuth={setAuth} /> : <Navigate to=\"/dashboard\" />}\n              />\n              <Route\n                path=\"/dashboard\"\n                element={isAuthenticated ? <Dashboard setAuth={setAuth} /> : <Navigate to=\"/login\" />}\n              />\n              <Route\n                path=\"/\"\n                element={<Navigate to={isAuthenticated ? \"/dashboard\" : \"/login\"} />}\n              />\n            </Routes>\n          </div>\n        </main>\n      </div>\n    </Router>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,OAAOC,KAAK,MAAM,oBAAoB;AACtC,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,MAAM,MAAM,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAE7DC,SAAS,CAAC,MAAM;IACd;IACA,MAAMgB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAIF,KAAK,EAAE;MACTD,kBAAkB,CAAC,IAAI,CAAC;IAC1B;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMI,OAAO,GAAIC,OAAO,IAAK;IAC3BL,kBAAkB,CAACK,OAAO,CAAC;EAC7B,CAAC;EAED,oBACET,OAAA,CAACT,MAAM;IAAAmB,QAAA,eACLV,OAAA;MAAKW,SAAS,EAAC,eAAe;MAAAD,QAAA,gBAC5BV,OAAA,CAACF,MAAM;QAACK,eAAe,EAAEA,eAAgB;QAACK,OAAO,EAAEA;MAAQ;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC9Df,OAAA;QAAMW,SAAS,EAAC,cAAc;QAAAD,QAAA,eAC5BV,OAAA;UAAKW,SAAS,EAAC,WAAW;UAAAD,QAAA,eACxBV,OAAA,CAACR,MAAM;YAAAkB,QAAA,gBACLV,OAAA,CAACP,KAAK;cACJuB,IAAI,EAAC,QAAQ;cACbC,OAAO,EAAE,CAACd,eAAe,gBAAGH,OAAA,CAACL,KAAK;gBAACa,OAAO,EAAEA;cAAQ;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGf,OAAA,CAACN,QAAQ;gBAACwB,EAAE,EAAC;cAAY;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxF,CAAC,eACFf,OAAA,CAACP,KAAK;cACJuB,IAAI,EAAC,WAAW;cAChBC,OAAO,EAAE,CAACd,eAAe,gBAAGH,OAAA,CAACJ,QAAQ;gBAACY,OAAO,EAAEA;cAAQ;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGf,OAAA,CAACN,QAAQ;gBAACwB,EAAE,EAAC;cAAY;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3F,CAAC,eACFf,OAAA,CAACP,KAAK;cACJuB,IAAI,EAAC,YAAY;cACjBC,OAAO,EAAEd,eAAe,gBAAGH,OAAA,CAACH,SAAS;gBAACW,OAAO,EAAEA;cAAQ;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGf,OAAA,CAACN,QAAQ;gBAACwB,EAAE,EAAC;cAAQ;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvF,CAAC,eACFf,OAAA,CAACP,KAAK;cACJuB,IAAI,EAAC,GAAG;cACRC,OAAO,eAAEjB,OAAA,CAACN,QAAQ;gBAACwB,EAAE,EAAEf,eAAe,GAAG,YAAY,GAAG;cAAS;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb;AAACb,EAAA,CA5CQD,GAAG;AAAAkB,EAAA,GAAHlB,GAAG;AA8CZ,eAAeA,GAAG;AAAC,IAAAkB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}